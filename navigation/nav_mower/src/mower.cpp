#include "mower.hpp"

#include "mower_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <utility>

#define TEST 0

using namespace mower_msgs::msg;

namespace fescue_iox
{

NavigationMowerAlg::NavigationMowerAlg(const MowerAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("MowerAlg"))
{
    last_cooldown_time_ = std::chrono::steady_clock::now();
    last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
    last_mark_detection_time_ = std::chrono::steady_clock::now();
    last_grass_time_ = std::chrono::steady_clock::now();
    last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
    last_zero_velocity_time_ = std::chrono::steady_clock::now();
    exception_start_time_ = std::chrono::steady_clock::now();

    beacon_status_ = BeaconStatus(-1, 0);

    {
        master_region_explore_result_.is_exist = false;
        master_region_explore_result_.area = 0.0;
        master_region_explore_result_.perimeter = 0.0;
        master_region_explore_result_.charge_station_flag = false;
        master_region_explore_result_.beacon_id = -1;

        slave_region_explore_result_.is_exist = false;
        slave_region_explore_result_.area = 0.0;
        slave_region_explore_result_.perimeter = 0.0;
        slave_region_explore_result_.charge_station_flag = false;
        slave_region_explore_result_.beacon_id = -1;
    }

    SetMowerAlgParam(param);
    InitPublisher();
    InitSlipDetection();
    InitStuckDetectionRecovery();
}

NavigationMowerAlg::~NavigationMowerAlg()
{
    DeinitSlipDetection();
    DeinitStuckDetectionRecovery();
    LOG_WARN("NavigationMowerAlg exit!");
}

void NavigationMowerAlg::InitSlipDetection()
{
    slip_detection_running_.store(true);
    slip_detection_thread_ = std::thread(&NavigationMowerAlg::SlipDetectionThread, this);
}

void NavigationMowerAlg::DeinitSlipDetection()
{
    slip_detection_running_.store(false);
    if (slip_detection_thread_.joinable())
    {
        slip_detection_thread_.join();
    }
}

void NavigationMowerAlg::SlipDetectionThread()
{
    while (slip_detection_running_.load())
    {
        // 获取最新的电机速度和运动检测数据
        MotorSpeedData motor_speed_data;
        MotionDetectionResult motion_detection_result;

        {
            std::lock_guard<std::mutex> lock(motor_speed_mtx_);
            motor_speed_data = motor_speed_data_;
        }

        {
            std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
            motion_detection_result = motion_detection_result_;
        }

        // 执行打滑检测
        bool current_slip = IsWheelSlipping(motor_speed_data, motion_detection_result, wheel_radius_, wheel_base_);
        LOG_DEBUG("[MowerAlg] [SlipDetectionThread] current_slip({})", current_slip);

        if (current_slip)
        {
            LOG_DEBUG("[MowerAlg] [SlipDetectionThread] slip detected!");

            if (!is_slipping_detected_.exchange(true)) // 第一次检测到打滑时，记录打滑开始的时间
            {
                slip_start_time_ = std::chrono::steady_clock::now();
            }

            // SetSlippingStatus(true); // 设置打滑状态

            auto slip_duration = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::steady_clock::now() - slip_start_time_);

            if (slip_duration.count() >= 10)
            {
                LOG_WARN("[MowerAlg] [SlipDetectionThread] (WARNING) slip detected for {} seconds!", slip_duration.count());

                PublishSlipException(SocExceptionLevel::WARNING,
                                     SocExceptionValue::ALG_PNC_MOWING_SLIPPING_EXCEPTION);
            }
        }
        else
        {
            LOG_DEBUG("[MowerAlg] [SlipDetectionThread] slip not detected!");

            if (is_slipping_detected_.exchange(false)) // 从打滑恢复
            {
                // SetSlippingStatus(false); // 清除打滑状态
            }

            // SetSlippingStatus(false); // 清除打滑状态
        }

        // 控制检测频率
        // std::this_thread::sleep_for(std::chrono::milliseconds(1000 / slip_detection_frequency_));
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void NavigationMowerAlg::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_ = motor_speed_data;

    // 将电机数据传递给脱困检测系统
    if (stuck_detection_recovery_)
    {
        stuck_detection_recovery_->SetMotorSpeedData(motor_speed_data);
    }

    // LOG_DEBUG("[MowerAlg] [SetMotorSpeedData] motor_speed_left({}), motor_speed_right({})",
    //           motor_speed_data.motor_speed_left, motor_speed_data.motor_speed_right);
}

void NavigationMowerAlg::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_ = motion_detection_result;
    // LOG_DEBUG("[MowerAlg] [SetMotionDetectionResult] is_motion({})", motion_detection_result.is_motion);
}

void NavigationMowerAlg::SetImuData(const ImuData &imu_data)
{
    std::lock_guard<std::mutex> lock(imu_data_mtx_);
    imu_data_ = imu_data;

    // 将IMU数据传递给脱困检测系统
    if (stuck_detection_recovery_)
    {
        stuck_detection_recovery_->SetImuData(imu_data);
    }
}

bool NavigationMowerAlg::IsWheelSlipping(const MotorSpeedData &motor_data,
                                         const MotionDetectionResult &motion_detection_result,
                                         float wheel_radius, float wheel_base)
{
    // LOG_ERROR("[MowerAlg] [IsWheelSlipping1] motor_timestamp({}), last_imu_timestamp_{}", motor_data.system_timestamp, last_imu_timestamp_);
    if (motor_data.system_timestamp - last_imu_timestamp_ < 0)
    {
        return false;
    }

    last_imu_timestamp_ = motor_data.system_timestamp;
    motion_detection_timestamp_ = motion_detection_result.timestamp;

    static bool is_slipping = false; // 跟踪当前是否打滑
    static int slip_counter = 0;
    const int slip_threshold = 200;

    // 将电机转速从RPM转换为rad/s
    float w_left = motor_data.motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_data.motor_speed_right * 2 * M_PI / 60.0f;
    // LOG_ERROR("[MowerAlg] [IsWheelSlipping1] w_left({}), w_right({})", w_left, w_right);

    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;

    float act_linear = (v_right + v_left) / 2.0f;
    float act_angular = (v_right - v_left) / wheel_base;

    // bool is_data_synced =
    //     std::abs(static_cast<int64_t>(motor_data.system_timestamp - motion_detection_result.timestamp)) < 1000; // ms

    // if (!is_data_synced)
    // {
    //     LOG_ERROR("[MowerAlg] [IsWheelSlipping1] 时间同步检查失败");
    //     return false;
    // }

    bool is_has_speed = (fabs(act_linear) > min_valid_linear_) || (fabs(act_angular) > min_valid_angular_);
    bool potential_slip = is_has_speed && !motion_detection_result.is_motion;

    if (!is_slipping)
    {
        if (potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = true;
            slip_counter = 0;
            LOG_INFO("[MowerAlg] [IsWheelSlipping1] 检测到打滑状态");
        }
    }
    else // is_slipping == true
    {
        if (!potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = false;
            slip_counter = 0;
            LOG_INFO("[MowerAlg] [IsWheelSlipping1] 打滑状态结束");
        }
    }

    LOG_DEBUG("[MowerAlg] [IsWheelSlipping1] act_linear({}), act_angular({}), is_motion({}), is_slipping({}), slip_counter({})",
              act_linear, act_angular, motion_detection_result.is_motion, is_slipping, slip_counter);

    return is_slipping;
}

void NavigationMowerAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

const char *NavigationMowerAlg::GetVersion()
{
    return "V1.1.0";
}

void NavigationMowerAlg::SetChargePileDockStatus(bool status)
{
    // LOG_INFO("[BeaconDetection] charge_pile_dock_status({})", status);
}

void NavigationMowerAlg::SetMCUSensor(const mower_msgs::msg::McuSensor &data)
{
    is_power_connected_ = data.charge_terminal_status;
    // LOG_INFO("[BeaconDetection] charge_terminal_status({})", is_power_connected_);
}

void NavigationMowerAlg::SetMowerComplete(const bool &mower_completed)
{
    LOG_DEBUG("[BeaconDetection] [Run] Mower complete!");
    mower_completed_ = mower_completed;
}

void NavigationMowerAlg::SetGrassDetecteStatus(const GrassDetectStatus &data)
{
    grass_detect_status_ = data;
    LOG_DEBUG("[SetGrassDetecteStatus] grass_detect_status_({})", int(grass_detect_status_));

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (IsGrassField(grass_detect_status_)) // 在草地上
        {
            is_on_grass_field_ = true;
            LOG_DEBUG("[SetGrassDetecteStatus] Is on grass field!");

            is_first_non_grass_detection_ = true; // 重置非草地计时器
            last_grass_time_ = std::chrono::steady_clock::now();
        }
        else // 不在草地上
        {
            is_on_grass_field_ = false;
            LOG_DEBUG("[SetGrassDetecteStatus] Is not on grass field!");

            auto current_time = std::chrono::steady_clock::now();
            if (is_first_non_grass_detection_)
            {
                last_grass_time_ = current_time;
                is_first_non_grass_detection_ = false;
            }
            non_grass_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_grass_time_);
            LOG_INFO_THROTTLE(1000, "[SetGrassDetecteStatus] 非草地持续时间：{} 秒", non_grass_duration_.count());
        }
    }
}

void NavigationMowerAlg::SetAllTaskClose()
{
    thread_control_ = ThreadControl::CLOSE_ALL_TASK;
    UpdateFeatureSelection(thread_control_);
    ResetMowerAlgFlags();
}

void NavigationMowerAlg::Test(CrossRegionRunningState cross_region_state)
{
    /**
    ControlRotaryMotion(-M_PI, 0.0, 1.0);
    ControlRotaryMotion(-M_PI, 0.0, 1.0);
    PublishVelocity(0.0, 0.0, 2000);
    // PublishVelocity(0, 1.0, 700);
    ControlRotaryMotion(0.14, 0.0, 1.0);
    PublishVelocity(0.0, 0.0, 2000);
    std::this_thread::sleep_for(std::chrono::seconds(10));
    // ControlRotaryMotion(-M_PI, 0.0, 0.3);
    // std::this_thread::sleep_for(std::chrono::seconds(10));
     */

    //! 1. 第一阶段。开启螺旋割草功能
    if (!mcu_triggers_cross_region_ &&
        !mcu_triggers_mower_ &&
        mcu_triggers_spiral_mower_)
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 开始螺旋割草模式!");
        thread_control_ = ThreadControl::SPIRAL_MOWING_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

    //! 2. 第二阶段。开启随机割草功能。MCU 未发布跨区域或回充请求
    if (!mcu_triggers_cross_region_ && /* MCU 没有跨区域请求 */
        mcu_triggers_mower_)           /* MCU 割草请求 */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 开始割草模式!");
        PerformRandomMowing();
    }

    //! 3. 第三阶段。MCU 发布跨区域请求
    if (mcu_triggers_cross_region_ && /* MCU 跨区域请求 */
        !mcu_triggers_mower_)         /* MCU 没有割草请求 */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 开启跨区域模式!");

        // 执行跨通道线程，关闭沿边线程
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

MowerAlgResult NavigationMowerAlg::Run(const MarkLocationResult &mark_loc_result,
                                       CrossRegionRunningState cross_region_state,
                                       const QRCodeLocationResult &qrcode_loc_result,
                                       const PerceptionFusionResult &fusion_result,
                                       RechargeRunningState recharge_state,
                                       McuExceptionStatus &mcu_exception_status,
                                       BehaviorRunningState &behavior_state)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        ShowMowerRunningInfo(mark_loc_result);
        LOG_WARN_THROTTLE(3000, "Mower Alg Run() is PAUSE!");

        return MowerAlgResult(false);
    }

    if (mower_completed_)
    {
        return MowerAlgResult(true);
    }

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (is_region_explore_mode_start_)
        {
            RegionExploreResult region_explore_result;
            {
                region_explore_result.result = true;
                region_explore_result.timestamp = GetTimestampMs();
                region_explore_result.master_region_map_result = master_region_explore_result_;
                region_explore_result.slave_region_map_result = slave_region_explore_result_;
            }

            if (region_explore_result_callback_)
            {
                region_explore_result_callback_(region_explore_result);
            }

            is_region_explore_mode_start_ = false;
        }
    }

    if (mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (is_cut_border_mode_start_)
        {
            if (cut_border_result_callback_)
            {
                cut_border_result_callback_(true, true);
            }

            is_cut_border_mode_start_ = false;
        }
    }

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        HandleSelfCheckAndOperation(mark_loc_result, cross_region_state,
                                    qrcode_loc_result, fusion_result, recharge_state,
                                    mcu_exception_status, behavior_state);
    }

    return MowerAlgResult(mower_completed_);
}

//==============================================================================
// Self Checking
//==============================================================================

void NavigationMowerAlg::HandleSelfCheckAndOperation(const MarkLocationResult &mark_loc_result,
                                                     CrossRegionRunningState cross_region_state,
                                                     const QRCodeLocationResult &qrcode_loc_result,
                                                     const PerceptionFusionResult &fusion_result,
                                                     RechargeRunningState recharge_state,
                                                     McuExceptionStatus &mcu_exception_status,
                                                     BehaviorRunningState &behavior_state)
{
    if (!is_self_checking_)
    {
        is_self_checking_ = true;
        is_self_recovery_active_ = false;
        self_check_start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("[BeaconDetection] 开始自检...");

        SetUndockResult(false, false, mower_msgs::srv::UndockOperationStatus::NOT_INTERRUPTIBLE);

        if (area_calc_start_callback_)
        {
            area_calc_start_callback_(GetTimestampMs());
        }
    }
    else
    {
        auto current_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - self_check_start_time_);

        if (is_self_recovery_active_)
        {
            LOG_INFO_THROTTLE(1000, "[BeaconDetection] 自检恢复进行中...");
            return;
        }
        else if (duration.count() >= SELF_CHECK_DURATION_SECONDS)
        {
            if (is_self_success_)
            {
                NormalOperation(mark_loc_result, cross_region_state,
                                qrcode_loc_result, fusion_result, recharge_state,
                                mcu_exception_status, behavior_state);
            }
            else
            {
                LOG_WARN("[BeaconDetection] 首次自检失败，开始自检恢复过程");

                is_self_recovery_active_ = true;

                std::thread recovery_thread([this]() {
                    bool recovery_result = PerformSelfCheckRecovery();

                    is_self_recovery_active_ = false;
                    is_self_success_ = recovery_result;

                    if (recovery_result)
                    {
                        LOG_INFO("[BeaconDetection] 自检恢复成功，将继续执行正常作业");
                    }
                    else
                    {
                        LOG_WARN("[BeaconDetection] 自检恢复失败，不执行正常作业");
                    }
                });

                recovery_thread.detach();
            }
        }
        else
        {
            LOG_INFO_THROTTLE(1000, "[BeaconDetection] 自检进行中，剩余时间：{} 秒", SELF_CHECK_DURATION_SECONDS - duration.count());
            is_self_success_ = SelfChecking();
        }
    }
}

bool NavigationMowerAlg::SelfChecking()
{
    // 1. 检查未充电异常状态
    // 2. 检查非草地异常状态
    if (!is_power_connected_ &&
        !is_on_grass_field_ && non_grass_duration_.count() >= SELF_CHECK_NON_GRASS_THRESHOLD_SECONDS)
    {
        LOG_WARN("[BeaconDetection] 启动检测到非草地超过 {} 秒", SELF_CHECK_NON_GRASS_THRESHOLD_SECONDS);

        return false;
    }

    return true;
}

bool NavigationMowerAlg::PerformSelfCheckRecovery()
{
    uint64_t recovery_duration_ms = static_cast<uint64_t>(1000.0 * self_recovery_distance_ / std::abs(self_recovery_linear_speed_));

    LOG_INFO("[BeaconDetection] 自检失败，开始回退 {} 米，预计耗时 {} 毫秒",
             self_recovery_distance_, recovery_duration_ms);

    PublishVelocity(self_recovery_linear_speed_, 0.0);

    auto recovery_start_time = std::chrono::steady_clock::now();

    auto last_check_time = std::chrono::steady_clock::now();

    while (true)
    {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - recovery_start_time).count();
        LOG_INFO("[BeaconDetection] 自检进行中，已回退 {} 毫秒", elapsed_ms);

        PublishVelocity(self_recovery_linear_speed_, 0.0);

        if (elapsed_ms >= recovery_duration_ms)
        {
            PublishVelocity(0.0, 0.0, 1000);
            LOG_INFO("[BeaconDetection] 回退完成，总计回退 {} 毫秒", elapsed_ms);

            if (!SelfChecking())
            {
                LOG_WARN("[BeaconDetection] 回退后自检仍然失败，上报异常状态");
                SetAllTaskClose();

                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_SELF_CHECK_FAILED_EXCEPTION);
                return false;
            }
            else
            {
                LOG_INFO("[BeaconDetection] 回退后自检成功");
                return true;
            }
        }

        if (std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - last_check_time)
                .count() >= 100)
        {
            last_check_time = current_time;

            if (SelfChecking())
            {
                PublishVelocity(0.0, 0.0, 1000);
                LOG_INFO("[BeaconDetection] 自检成功，回退过程中自检成功，已回退 {} 毫秒", elapsed_ms);
                return true;
            }
            else
            {
                LOG_WARN("[BeaconDetection] 自检失败，回退过程中自检失败，已回退 {} 毫秒", elapsed_ms);
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    return false;
}
//==============================================================================
// Self Checking
//==============================================================================

void NavigationMowerAlg::NormalOperation(const MarkLocationResult &mark_loc_result,
                                         CrossRegionRunningState cross_region_state,
                                         const QRCodeLocationResult &qrcode_loc_result,
                                         const PerceptionFusionResult &fusion_result,
                                         RechargeRunningState recharge_state,
                                         McuExceptionStatus &mcu_exception_status,
                                         BehaviorRunningState &behavior_state)
{
#if (TEST == 1)

    ShowMowerRunningInfo(mark_loc_result);

    if (mcu_triggers_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_spiral_mower_)
    {
        Test(cross_region_state);
    }

#else

    ShowExplorePrint(behavior_state);
    ShowMowerRunningInfo(mark_loc_result);

    if (mcu_triggers_cut_border_)
    {
        is_cut_border_mode_start_ = true;
        CutBorderModule(mark_loc_result, cross_region_state,
                        qrcode_loc_result, fusion_result, recharge_state,
                        mcu_exception_status, behavior_state);
    }
    else
    {
        if (mcu_triggers_region_exploration_) // 区域探索
        {
            is_region_explore_mode_start_ = true;
            RegionExplorationModule(mark_loc_result, cross_region_state,
                                    qrcode_loc_result, fusion_result, recharge_state,
                                    mcu_exception_status, behavior_state);
        }
        else // 正常作业逻辑
        {
            NormalMowingModule(mark_loc_result, cross_region_state,
                               qrcode_loc_result, fusion_result, recharge_state,
                               mcu_exception_status, behavior_state);
        }
    }

#endif
}

void NavigationMowerAlg::CutBorderModule(const MarkLocationResult &mark_loc_result,
                                         CrossRegionRunningState cross_region_state,
                                         const QRCodeLocationResult &qrcode_loc_result,
                                         const PerceptionFusionResult &fusion_result,
                                         RechargeRunningState recharge_state,
                                         McuExceptionStatus &mcu_exception_status,
                                         BehaviorRunningState &behavior_state)
{
    // 第一步，退桩
    // ProcessCutBorderUnstakeMode();
    ProcessCutBorderUnstakeMode(qrcode_loc_result);

    // 只在首次进入时初始化
    if (is_first_enter_cut_border_last_qr_detection_time_)
    {
        last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now(); // 退桩后，充电桩二维码检测开始计时
        is_first_enter_cut_border_last_qr_detection_time_ = false;

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

    // 检查脱困状态
    LOG_INFO("[CutBorderModule] IsStuckDetected: {}, IsStuckRecoveryActive: {}", IsStuckDetected(), IsStuckRecoveryActive());
    if (IsStuckDetected() && !IsStuckRecoveryActive())
    {
        LOG_WARN("[CutBorderModule] 检测到被困状态，开始脱困恢复");
        StartStuckRecovery();
    }

    // 如果正在脱困恢复中，检查是否已恢复
    if (IsStuckRecoveryActive())
    {
        LOG_INFO_THROTTLE(1000, "[CutBorderModule] 脱困恢复进行中...");
        return;
    }

    if (is_slipping_detected_.load())
    {
        HandleCutBorderMcuException(recharge_state, cross_region_state);
        LOG_INFO("[CutBorderModule1] 检测到打滑，进入异常处理");
    }
    else
    {
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleCutBorderMcuException(recharge_state, cross_region_state);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            // 边切功能
            PerformCutBorder(mark_loc_result, qrcode_loc_result, cross_region_state, behavior_state);
            break;
        }
        default:
            break;
        }
    }
}

void NavigationMowerAlg::RegionExplorationModule(const MarkLocationResult &mark_loc_result,
                                                 CrossRegionRunningState cross_region_state,
                                                 const QRCodeLocationResult &qrcode_loc_result,
                                                 const PerceptionFusionResult &fusion_result,
                                                 RechargeRunningState recharge_state,
                                                 McuExceptionStatus &mcu_exception_status,
                                                 BehaviorRunningState &behavior_state)
{
    // 第一步，退桩。不需要进行MCU异常状态处理
    // ProcessExplorationUnstakeMode();
    ProcessExplorationUnstakeMode(qrcode_loc_result);

    // 只在首次进入时初始化
    if (is_first_enter_explore_last_qr_detection_time_)
    {
        last_qr_explore_detection_time_ = std::chrono::steady_clock::now(); // 退桩后，充电桩二维码检测开始计时
        is_first_enter_explore_last_qr_detection_time_ = false;

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

    // 检查脱困状态
    LOG_INFO("[RegionExplorationModule] IsStuckDetected: {}, IsStuckRecoveryActive: {}", IsStuckDetected(), IsStuckRecoveryActive());
    if (IsStuckDetected() && !IsStuckRecoveryActive())
    {
        LOG_WARN("[RegionExplorationModule] 检测到被困状态，开始脱困恢复");
        StartStuckRecovery();
    }

    // 如果正在脱困恢复中，检查是否已恢复
    if (IsStuckRecoveryActive())
    {
        LOG_INFO_THROTTLE(1000, "[RegionExplorationModule] 脱困恢复进行中...");
        return;
    }

    if (is_slipping_detected_.load())
    {
        HandleExplorationMcuException(recharge_state, cross_region_state);
        LOG_INFO("[RegionExplorationModule] 检测到打滑，进入异常处理");
    }
    else
    {
        // 根据MCU异常状态进行不同处理
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleExplorationMcuException(recharge_state, cross_region_state);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            // 区域探索功能
            PerformExploration(mark_loc_result, qrcode_loc_result, cross_region_state, behavior_state);
            break;
        }
        default:
            break;
        }
    }
}

void NavigationMowerAlg::NormalMowingModule(const MarkLocationResult &mark_loc_result,
                                            CrossRegionRunningState cross_region_state,
                                            const QRCodeLocationResult &qrcode_loc_result,
                                            const PerceptionFusionResult &fusion_result,
                                            RechargeRunningState recharge_state,
                                            McuExceptionStatus &mcu_exception_status,
                                            BehaviorRunningState &behavior_state)
{
    if (is_power_connected_) // 接电
    {
        // ProcessNormalOperationUnstakeMode();
        ProcessNormalOperationUnstakeMode(qrcode_loc_result);
    }
    else // 未接电
    {
        if (!is_unstaking_)
        {
            PreProcessingMowing(qrcode_loc_result);
        }
    }

    // 检查脱困状态
    LOG_INFO("[NormalMowingModule] IsStuckDetected: {}, IsStuckRecoveryActive: {}", IsStuckDetected(), IsStuckRecoveryActive());
    if (IsStuckDetected() && !IsStuckRecoveryActive())
    {
        LOG_WARN("[NormalMowingModule] 检测到被困状态，开始脱困恢复");
        StartStuckRecovery();
    }

    // 如果正在脱困恢复中，检查是否已恢复
    if (IsStuckRecoveryActive())
    {
        LOG_INFO_THROTTLE(1000, "[NormalMowingModule] 脱困恢复进行中...");
        return;
    }

    if (is_slipping_detected_.load())
    {
        HandleMcuException(recharge_state, cross_region_state);
        LOG_INFO_THROTTLE(1000, "[NormalMowingModule1] 检测到打滑，进入异常处理");
    }
    else
    {
        // 根据MCU异常状态进行不同处理
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleMcuException(recharge_state, cross_region_state);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            HandleNormalOperation(cross_region_state, recharge_state, behavior_state);
            break;
        }
        default:
            break;
        }
    }
}

//==============================================================================
// 区域探索功能处理函数
//==============================================================================
void NavigationMowerAlg::PerformExploration(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                                            CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于沿边和未定义模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于沿边和未定义模式");

        bool is_beacon_valid = false; // 默认为信标无效
        ProcessBeaconDetection(mark_loc_result, enter_multi_region_exploration_, is_beacon_valid);

        if (!enter_multi_region_exploration_)
        {
            ProcessSingleAreaExplorationMode(qrcode_loc_result, enter_multi_region_exploration_);
        }
        else
        {
            ProcessMultiAreaExplorationMode(mark_loc_result, enter_multi_region_exploration_, is_beacon_valid);
        }

        break;
    }

    case ThreadControl::CROSS_REGION_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于跨区域模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于跨区域模式");

        // 根据跨区域返回的不同状态进行处理
        HandleExploreCrossRegionStates(cross_region_state);

        break;
    }

    case ThreadControl::RECHARGE_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于回充模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于回充模式");

        ProcessingExplorationRecharge(qrcode_loc_result);

        if (is_first_region_explore_mode_end_)
        {
            PublishVelocity(0.0, 0.0, 1000);
            is_first_region_explore_mode_end_ = false;
        }
        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于Behavior模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于Behavior模式");

        CheckVelocityAndUpdateState(behavior_state);

        break;
    }

    default:
        // LOG_INFO("[BeaconDetection] 处于其他功能模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于其他功能模式");

        break;
    }
}

/**
 * @brief 持续检测信标状态
 *
 * @param mark_loc_result
 * @param qrcode_loc_result
 * @param is_beacon_valid
 */
void NavigationMowerAlg::ProcessBeaconDetection(const MarkLocationResult &mark_loc_result,
                                                bool &enter_multi_region_exploration,
                                                bool &is_beacon_valid)
{
    if (!is_enable_unstake_mode_ || is_unstake_success_) /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] 开始区域探索模式");

        if (mark_loc_result.mark_perception_status == 0) // 感知没有检测到信标
        {
            LOG_DEBUG("[BeaconDetection] 1. 感知没有检测到信标, 开启沿边");

            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
        }
        else // 感知检测到信标
        {
            LOG_DEBUG("[BeaconDetection] 2. 感知检测到信标");

            if (mark_loc_result.mark_id_distance.size() <= 0) // 定位的mark_id_distance没值
            {
                LOG_DEBUG("[BeaconDetection] 2.1 定位的mark_id_distance无值");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
            }
            else // 定位的mark_id_distance有值
            {
                LOG_DEBUG("[BeaconDetection] 2.2 定位的mark_id_distance有值");

                // 判断信标是否有效。（mark_id_distance小于50cm认为当前信标有效）
                int shortest_dis_inx = -1;
                std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
                FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

                if (shortest_dis_inx == -1) // 若信标无效。不操作，继续之前的动作
                {
                    LOG_DEBUG("[BeaconDetection] 2.2.1 跨区信标无效");
                    HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
                }
                else // 若信标有效。检测栈容器是否为空
                {
                    LOG_DEBUG("[BeaconDetection] 2.2.2 跨区信标有效");
                    LOG_DEBUG("[BeaconDetection] 2.2.2 有效的信标为 mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    //! 信标有效。可以进入多区域探索
                    current_mark_id_ = mark_id_distance_vec[shortest_dis_inx].mark_id;
                    if (first_detection_beacon_)
                    {
                        beacon_status_ = BeaconStatus(current_mark_id_, 1);
                        first_detection_beacon_ = false;
                    }

                    enter_multi_region_exploration = true;
                    is_beacon_valid = true;
                }
            }
        }
    }
}

/**
 * @brief 利用充电桩检测判断沿边停止转回充
 *
 * @param mark_loc_result
 * @param qrcode_loc_result
 * @param is_beacon_valid
 */
void NavigationMowerAlg::ProcessSingleAreaExplorationMode(const QRCodeLocationResult &qrcode_loc_result,
                                                          const bool &enter_multi_region_exploration)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        !enter_multi_region_exploration)                     /* 不进入多区域探索*/
    {
        // LOG_INFO("[BeaconDetection] 开始单区域探索模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 开始单区域探索模式");

        if (qrcode_loc_result.detect_status != QRCodeDetectStatus::NO_DETECT_QRCODE)
        {
            auto current_time = std::chrono::steady_clock::now();
            qr_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_qr_explore_detection_time_);

            LOG_DEBUG("[BeaconDetection] 充电桩二维码检测冷却的计时（秒）：({})", qr_detection_duration_.count());
            if (qr_detection_duration_.count() > qr_detection_cooldown_time_threshold_) // beacon_detection_cooldown_time_ 增加计时器
            {
                qr_code_detection_count_++;
                last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
                LOG_DEBUG("[BeaconDetection] 检测到充电桩二维码位姿有效，当前检测次数: {}", qr_code_detection_count_);
            }
        }

        // 利用二维码检测判断沿边停止转回充
        if (qr_code_detection_count_ >= 2)
        {
            LOG_INFO("[BeaconDetection] 检测到回充二维码位姿有效两次，进入回充模式");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            is_single_area_recharge_ = true;

            // 重置状态
            qr_code_detection_count_ = 1;

            // float area = 0.0;
            // float perimeter = 0.0;
            // if (area_calc_stop_callback_)
            // {
            //     area_calc_stop_callback_(GetTimestampMs(), area, perimeter);
            // }

            // LOG_INFO("[BeaconDetection] area: {} , perimeter: {}", area, perimeter);

            // {
            //     master_region_explore_result_.is_exist = true;
            //     master_region_explore_result_.area = area;
            //     master_region_explore_result_.perimeter = perimeter;
            //     master_region_explore_result_.charge_station_flag = true;
            //     master_region_explore_result_.beacon_id = -1;

            //     slave_region_explore_result_.is_exist = false;
            // }
        }
    }
}

void NavigationMowerAlg::ProcessMultiAreaExplorationMode(const MarkLocationResult &mark_loc_result,
                                                         const bool &enter_multi_region_exploration,
                                                         bool &is_beacon_valid)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        enter_multi_region_exploration)                      /* 进入多区域探索*/
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 开始多区域探索模式");

        if (is_beacon_valid) // 信标有效
        {
            // 重置冷却时间戳，并激活冷却机制
            last_cooldown_time_ = std::chrono::steady_clock::now();
            is_cooldown_active_ = true;
            // ResetAndActivateCooldown();

            LOG_INFO("[BeaconDetection] 检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
            LOG_INFO("[BeaconDetection] 当前检测mark_id: {}", current_mark_id_);

            // 只在首次进入时初始化
            if (is_first_enter_last_mark_detection_time_)
            {
                last_mark_detection_time_ = std::chrono::steady_clock::now(); // 信标检测开始计时
                is_first_enter_last_mark_detection_time_ = false;
                LOG_DEBUG("[BeaconDetection] 信标检测开始计时");

                // stop
                float area = 0.0;
                float perimeter = 0.0;
                if (area_calc_stop_callback_)
                {
                    area_calc_stop_callback_(GetTimestampMs(), area, perimeter);
                }

                {
                    master_region_explore_result_.is_exist = false;
                    slave_region_explore_result_.is_exist = false;
                }

                // start
                if (area_calc_start_callback_)
                {
                    area_calc_start_callback_(GetTimestampMs());
                }
            }

            auto current_time = std::chrono::steady_clock::now();
            mark_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_mark_detection_time_);

            auto current_time_sec = std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
            auto last_mark_detection_time_sec = std::chrono::duration_cast<std::chrono::seconds>(last_mark_detection_time_.time_since_epoch());
            LOG_WARN("[BeaconDetection]  当前时间戳（秒）current_time_sec({})", current_time_sec.count());
            LOG_WARN("[BeaconDetection] 上一时间戳（秒）last_mark_detection_time_sec ({})", last_mark_detection_time_sec.count());
            LOG_DEBUG("[BeaconDetection] 信标检测冷却的计时（秒）：({})", mark_detection_duration_.count());
            if (mark_detection_duration_.count() > mark_detection_cooldown_time_threshold_)
            {
                if (current_mark_id_ == beacon_status_.mark_id) // 同一个mark_id
                {
                    beacon_status_.beacon_look_count++;
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection] 检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection] 同一个mark_id, 当前检测mark_id: {}", current_mark_id_);
                }
                else // 不同mark_id
                {
                    beacon_status_ = BeaconStatus(current_mark_id_, 1);
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection] 检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection] 不同mark_id, 当前检测mark_id: {}", current_mark_id_);
                    LOG_WARN("[BeaconDetection] 不同mark_id, 上一检测mark_id: {}", beacon_status_.mark_id);
                }
            }
        }

        if (beacon_status_.beacon_look_count >= 2)
        {
            // 启动跨区域进程
            LOG_INFO("[BeaconDetection] 信标检测超过两次，启动跨区域进程");

            thread_control_ = ThreadControl::CROSS_REGION_THREAD;
            UpdateFeatureSelection(thread_control_);
            EdgeFollowDisable();
            is_single_area_recharge_ = false;

            // 重置状态
            next_paired_beacon_id_ = PairNumber(current_mark_id_);
            beacon_status_ = BeaconStatus(next_paired_beacon_id_, 1);
            LOG_INFO("[BeaconDetection]下一对信标id为 {}", next_paired_beacon_id_);

            float area = 0.0;
            float perimeter = 0.0;
            if (area_calc_stop_callback_)
            {
                area_calc_stop_callback_(GetTimestampMs(), area, perimeter);
            }

            LOG_INFO("[BeaconDetection] area: {} , perimeter: {}", area, perimeter);

            if (is_master_region_) // 主区
            {
                {
                    master_region_explore_result_.is_exist = true;
                    master_region_explore_result_.area = area;
                    master_region_explore_result_.perimeter = perimeter;
                    master_region_explore_result_.charge_station_flag = true;
                    master_region_explore_result_.beacon_id = current_mark_id_;
                }

                is_master_region_ = false; // 下一个副区
            }
            else // 副区
            {
                {
                    slave_region_explore_result_.is_exist = true;
                    slave_region_explore_result_.area = area;
                    slave_region_explore_result_.perimeter = perimeter;
                    slave_region_explore_result_.charge_station_flag = false;
                    slave_region_explore_result_.beacon_id = current_mark_id_;
                }
            }
        }
        else
        {
            // 继续沿边
            LOG_INFO_THROTTLE(2000, "[BeaconDetection] 信标检测未超过两次，继续沿边");

            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();
        }
    }
}

/**
 * @brief 对正整数进行配对
 *
 * 规则说明：
 * 如果输入的正整数 n 为奇数，则返回 n+1；
 * 如果 n 为偶数，则返回 n-1。
 * 公式表示如下：
 * $$\text{若 } n \% 2 == 1, \text{ 则输出 } n+1$$
 * $$\text{若 } n \% 2 == 0, \text{ 则输出 } n-1$$
 *
 * @param n 正整数
 * @return int 配对后的数值
 */
int NavigationMowerAlg::PairNumber(int n)
{
    // 判断 n 是否为奇数
    if (n % 2 == 1)
    {
        // 如果 n 为奇数，返回 n+1
        return n + 1;
    }
    else
    {
        // 如果 n 为偶数，返回 n-1
        return n - 1;
    }
}

void NavigationMowerAlg::ProcessingExplorationRecharge(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_single_area_recharge_) // 单区域回充，基于回充条件上报
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE) // 可以计算充电桩二维码位姿
        {
            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < recharge_distance_threshold_)
            {
                //! 1. 获取单区域信息
                float area = 0.0;
                float perimeter = 0.0;
                if (area_calc_stop_callback_)
                {
                    area_calc_stop_callback_(GetTimestampMs(), area, perimeter);
                }

                LOG_INFO("[BeaconDetection] area: {} , perimeter: {}", area, perimeter);

                {
                    master_region_explore_result_.is_exist = true;
                    master_region_explore_result_.area = area;
                    master_region_explore_result_.perimeter = perimeter;
                    master_region_explore_result_.charge_station_flag = true;
                    master_region_explore_result_.beacon_id = -1;

                    slave_region_explore_result_.is_exist = false;
                }

                //! 2. 上报区域探索结果
                RegionExploreResult region_explore_result;
                {
                    region_explore_result.result = true;
                    region_explore_result.timestamp = GetTimestampMs();
                    region_explore_result.master_region_map_result = master_region_explore_result_;
                    region_explore_result.slave_region_map_result = slave_region_explore_result_;
                }

                if (region_explore_result_callback_)
                {
                    region_explore_result_callback_(region_explore_result);
                }

                is_region_explore_mode_start_ = false;
            }
        }
    }
    else // 多区域回充，直接上报
    {
        RegionExploreResult region_explore_result;
        {
            region_explore_result.result = true;
            region_explore_result.timestamp = GetTimestampMs();
            region_explore_result.master_region_map_result = master_region_explore_result_;
            region_explore_result.slave_region_map_result = slave_region_explore_result_;
        }

        if (region_explore_result_callback_)
        {
            region_explore_result_callback_(region_explore_result);
        }

        is_region_explore_mode_start_ = false;
    }
}

void NavigationMowerAlg::ResetAndActivateCooldown()
{
    // 重置冷却时间戳，并激活冷却机制
    last_cooldown_time_ = std::chrono::steady_clock::now();
    is_cooldown_active_ = true;
}
void NavigationMowerAlg::HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active)
{
    if (is_cooldown_active) // 冷却机制激活
    {
        LOG_DEBUG("[BeaconDetection] 冷却机制激活");

        auto current_time = std::chrono::steady_clock::now();
        edge_perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // 打印时间差
        LOG_DEBUG("[BeaconDetection] 沿边感知驱动冷却的计时（秒）：({})", edge_perception_drive_duration_.count());
        HandleEdgeCooldownMechanism(mark_loc_result, is_cooldown_active, edge_perception_drive_duration_, edge_perception_drive_cooldown_time_threshold_);
    }
    else // 冷却机制未激活
    {
        LOG_DEBUG("[BeaconDetection] 冷却机制未激活");

        LOG_DEBUG("[BeaconDetection] 开启感知驱动，同时关闭沿边");

        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);

        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationMowerAlg::HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                     std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold) // 感知检测结果
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_DEBUG("[BeaconDetection] 计时时间超过({})秒，冷却结束", perception_drive_cooldown_time_threshold);

        LOG_DEBUG("[BeaconDetection] 开启感知驱动，同时关闭沿边");

        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);

        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // 冷却未结束，跳过执行
        LOG_DEBUG("[BeaconDetection] 冷却时间未结束，未超过({})秒", perception_drive_cooldown_time_threshold);

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
        LOG_DEBUG("[BeaconDetection] 开启沿边");
    }
}

void NavigationMowerAlg::ProcessExplorationUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* 开启出桩模式 && 出桩未成功 */
        is_power_connected_ &&                             /* 小车接电*/
        mcu_triggers_region_exploration_)                  /* MCU 探索请求 */
    {
        LOG_INFO("[ProcessExplorationUnstakeMode] 探索模式。开始出桩模式");
        PerformUnstakeMode(); // 执行出桩操作
    }
}

void NavigationMowerAlg::ProcessExplorationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* 开启出桩模式 && 出桩未成功 */
        is_power_connected_ &&                             /* 小车接电*/
        mcu_triggers_region_exploration_)                  /* MCU 探索请求 */
    {
        LOG_INFO("[ProcessExplorationUnstakeMode] 探索模式。开始出桩模式");

        PerformUnstakeMode(qrcode_loc_result); // 执行出桩操作
    }
}

void NavigationMowerAlg::HandleExplorationMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state)
{
    // 如果检测到充电桩二维码状态
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        recharge_state == RechargeRunningState::PER_FOUND_QR_CODE) // 已经找到充电桩二维码 /**不可以采取恢复模式 */
    {
        ProcessExplorationRechargeException(recharge_state);
    }
    // 如果检测到信标二维码状态（跨区域情况）
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON &&
             cross_region_state != CrossRegionRunningState::UNDEFINED) // 已找到信标状态 /**不可以采取恢复模式 */
    {
        ProcessExplorationCrossRegionException(cross_region_state);
    }
    // 其他异常情况，进入恢复模式
    else
    {
        ProcessRecoveryException();
    }
}

// 处理MCU异常中回充请求逻辑
void NavigationMowerAlg::ProcessExplorationRechargeException(RechargeRunningState recharge_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] 检测到充电桩二维码");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        mcu_triggers_region_exploration_)                    /* MCU 探索请求 */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] 开启回充模式");

        // 切换到回充线程，并更新功能选择
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// 处理MCU异常中跨区域请求逻辑
void NavigationMowerAlg::ProcessExplorationCrossRegionException(CrossRegionRunningState cross_region_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] 检测到信标二维码");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        mcu_triggers_region_exploration_)                    /* MCU 探索请求 */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] 开启跨区域模式");

        // 切换到跨区域线程，并更新功能选择
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

void NavigationMowerAlg::ShowExplorePrint(BehaviorRunningState &behavior_state)
{
    LOG_INFO_THROTTLE(1500, "[BeaconDetection] 区域探索计数：({})", region_count_);
}

//==============================================================================
// 异常状态处理函数
//==============================================================================
void NavigationMowerAlg::HandleMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state)
{
    // 如果检测到充电桩二维码状态
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        recharge_state == RechargeRunningState::PER_FOUND_QR_CODE) // 已经找到充电桩二维码 /**不可以采取恢复模式 */
    {
        ProcessRechargeException(recharge_state);
    }
    // 如果检测到信标二维码状态（跨区域情况）
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON &&
             cross_region_state != CrossRegionRunningState::UNDEFINED) // 已找到信标状态 /**不可以采取恢复模式 */
    {
        ProcessCrossRegionException(cross_region_state);
    }
    // 其他异常情况，进入恢复模式
    else
    {
        ProcessRecoveryException();
        // if (is_slipping_detected_.load())
        // {
        //     ProcessRecoverySlipException();
        //     // SetSlippingStatus(true);
        // }
        // else
        // {
        //     ProcessRecoveryException();
        // }
    }
}

// 处理MCU异常中回充请求逻辑
void NavigationMowerAlg::ProcessRechargeException(RechargeRunningState recharge_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] 检测到充电桩二维码");

    // MCU 发布回充请求
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        mcu_triggers_recharge_ &&                            /* MCU 回充请求 */
        !mcu_triggers_cross_region_ &&                       /* MCU 没有跨区域请求 */
        !mcu_triggers_mower_ &&                              /* MCU 随机割草请求 */
        !mcu_triggers_spiral_mower_)                         /* MCU 螺旋割草请求 */

    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] 开启回充模式");

        // 切换到回充线程，并更新功能选择
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// 处理MCU异常中跨区域请求逻辑
void NavigationMowerAlg::ProcessCrossRegionException(CrossRegionRunningState cross_region_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] 检测到信标二维码");

    // MCU 发布跨区域请求
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        !mcu_triggers_recharge_ &&                           /* MCU 没有回充请求 */
        mcu_triggers_cross_region_ &&                        /* MCU 发布跨区域请求 */
        !mcu_triggers_mower_ &&                              /* MCU 随机割草请求 */
        !mcu_triggers_spiral_mower_)                         /* MCU 螺旋割草请求 */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] 开启跨区域模式");

        // 切换到跨区域线程，并更新功能选择
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// 处理其他异常情况，进入恢复模式
void NavigationMowerAlg::ProcessRecoveryException()
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] 处理碰撞或抬起异常");

    if ((!is_enable_unstake_mode_ || is_unstake_success_)) /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
    {
        thread_control_ = ThreadControl::BEHAVIOR_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// 处理其他异常情况，进入恢复模式
void NavigationMowerAlg::ProcessRecoverySlipException()
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] 处理打滑异常");

    // HandleRecoveryStart();

    thread_control_ = ThreadControl::BEHAVIOR_THREAD;
    UpdateFeatureSelection(thread_control_);
}

//==============================================================================
// 正常状态处理函数
//==============================================================================

// 根据正常状态下的不同阶段进行处理
void NavigationMowerAlg::HandleNormalOperation(CrossRegionRunningState cross_region_state, RechargeRunningState recharge_state,
                                               BehaviorRunningState &behavior_state)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    case ThreadControl::CROSS_REGION_THREAD:
    case ThreadControl::RECHARGE_THREAD:
    case ThreadControl::RANDOM_MOWING_THREAD:
    case ThreadControl::SPIRAL_MOWING_THREAD:
    {
        ProcessRandomMowing();
        ProcessSpiralMowing();
        ProcessCrossRegionMode(cross_region_state);
        ProcessRechargeMode(recharge_state);

        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于Behavior模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于Behavior模式");

        CheckVelocityAndUpdateState(behavior_state);

        break;
    }

    default:
        // LOG_INFO("[BeaconDetection] 处于其他功能模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于其他功能模式");

        break;
    }
}

void NavigationMowerAlg::CheckVelocityAndUpdateState(BehaviorRunningState &behavior_state)
{
    // 从电机速度获取当前线性速度和角速度
    MotorSpeedData motor_speed_data;
    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        motor_speed_data = motor_speed_data_;
    }

    float v_left = motor_speed_data.motor_speed_left * 2 * M_PI / 60.0f * wheel_radius_;   // 左轮速度 (m/s)
    float v_right = motor_speed_data.motor_speed_right * 2 * M_PI / 60.0f * wheel_radius_; // 右轮速度 (m/s)
    float act_linear = (v_right + v_left) / 2.0f;                                          // 实际线性速度 (m/s)
    float act_angular = (v_right - v_left) / wheel_base_;                                  // 实际角速度 (rad/s)

    // 检查速度是否接近零
    if (std::abs(act_linear) < linear_velocity_threshold_ &&
        std::abs(act_angular) < angular_velocity_threshold_)
    {
        if (!is_velocity_zero_)
        {
            // 速度首次接近零，开始计时
            last_zero_velocity_time_ = std::chrono::steady_clock::now();
            is_velocity_zero_ = true;
            LOG_DEBUG("[BeaconDetection] 速度接近零，开始计时");
        }
        else
        {
            // 速度持续接近零，检查持续时间
            auto current_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                current_time - last_zero_velocity_time_);

            if (duration.count() >= ZERO_VELOCITY_THRESHOLD_SECONDS)
            {
                // 速度接近零持续1秒，结束恢复模式
                LOG_ERROR("[BeaconDetection] 速度接近零持续1秒，恢复模式结束");
                thread_control_ = ThreadControl::UNDEFINED;
                UpdateFeatureSelection(thread_control_);
                is_velocity_zero_ = false; // 重置标志

                // HandleRecoveryEnd(); // 通知挂起的出桩任务继续执行
                // SetSlippingStatus(false);
            }
        }
    }
    else
    {
        // 速度超过阈值，重置计时器
        if (is_velocity_zero_)
        {
            LOG_DEBUG("[BeaconDetection] 速度不再接近零，重置计时器");
            is_velocity_zero_ = false;
        }
    }

    if (behavior_state == BehaviorRunningState::SUCCESS)
    {
        LOG_ERROR("[BeaconDetection] 行为状态SUCCESS，恢复模式结束");
        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);
        is_velocity_zero_ = false; // 重置标志

        // HandleRecoveryEnd(); // 通知挂起的出桩任务继续执行
        // SetSlippingStatus(false);
    }
}

// 第一阶段：出桩处理
void NavigationMowerAlg::ProcessNormalOperationUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* 开启出桩模式 && 出桩未成功 */
        is_power_connected_ &&                             /* 小车接电*/
        (mcu_triggers_recharge_ ||                         /* MCU 没有回充请求 */
         mcu_triggers_cross_region_ ||                     /* MCU 没有跨区域请求 */
         mcu_triggers_mower_ ||                            /* MCU 随机割草请求 */
         mcu_triggers_spiral_mower_))                      /* MCU 螺旋割草请求 */

    {
        LOG_INFO("[ProcessNormalOperationUnstakeMode1] 接电，开始出桩任务...");
        PerformUnstakeMode(); // 执行出桩操作
    }
}

// 第一阶段：出桩处理(二维码定位版本)
void NavigationMowerAlg::ProcessNormalOperationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* 开启出桩模式 && 出桩未成功 */
        is_power_connected_ &&                             /* 小车接电*/
        (mcu_triggers_recharge_ ||                         /* MCU 没有回充请求 */
         mcu_triggers_cross_region_ ||                     /* MCU 没有跨区域请求 */
         mcu_triggers_mower_ ||                            /* MCU 随机割草请求 */
         mcu_triggers_spiral_mower_))                      /* MCU 螺旋割草请求 */

    {
        LOG_INFO("[ProcessNormalOperationUnstakeMode1] 接电，开始出桩任务...");

        is_unstaking_ = true;

        PerformUnstakeMode(qrcode_loc_result); // 执行出桩操作
        // PerformUnstakeModeAsync(qrcode_loc_result);
    }
}

// 第一阶段：割草前处理
void NavigationMowerAlg::PreProcessingMowing(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* 开启出桩模式 && 出桩未成功 */
        !is_power_connected_ &&                            /* 小车接电*/
        (mcu_triggers_recharge_ ||                         /* MCU 没有回充请求 */
         mcu_triggers_cross_region_ ||                     /* MCU 没有跨区域请求 */
         mcu_triggers_mower_ ||                            /* MCU 随机割草请求 */
         mcu_triggers_spiral_mower_))                      /* MCU 螺旋割草请求 */

    {
        LOG_INFO("[PreProcessingMowing] 未接电，开始预处理割草逻辑");

        // todo

        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE &&                                    // 可以计算充电桩二维码位姿
            sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) <= mower_start_qr_distance_threshold_) // 0.5
        {
            ControlLinearMotion(unstake_distance_ / 2, 0.0, unstake_vel_linear_, -1); // 退出充电桩
            ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);      // 旋转45度背离充电桩
            PublishVelocity(0.0, 0.0, 1000);                                          // 停止运动1s

            if (is_on_grass_field_) // 在草地
            {
                is_unstake_mode_completed_ = true; // 出桩完成
                is_unstake_success_ = true;        // 出桩成功

                SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // 上报成功状态
                LOG_INFO("[BeaconDetection] 在草地上。出桩模式成功，开始割草模式");
            }
            else // 不在草地
            {
                is_unstake_mode_completed_ = true; // 出桩完成
                is_unstake_success_ = false;       // 出桩失败

                SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // 上报失败状态
                LOG_ERROR("[BeaconDetection] 不在草地上。出桩模式失败，上报错误状态");

                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
            }
        }
        else
        {
            is_unstake_mode_completed_ = true;
            is_unstake_success_ = true;
            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_);
        }
    }
}

// 第二阶段：随机割草处理
void NavigationMowerAlg::ProcessRandomMowing()
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        !mcu_triggers_recharge_ &&                           /* MCU 没有回充请求 */
        !mcu_triggers_cross_region_ &&                       /* MCU 没有跨区域请求 */
        mcu_triggers_mower_ &&                               /* MCU 随机割草请求 */
        !mcu_triggers_spiral_mower_)                         /* MCU 螺旋割草请求 */
    {
        // 检查割草期间的非草地状态
        if (!is_on_grass_field_ && non_grass_duration_.count() >= NON_GRASS_WARN_THRESHOLD_SECONDS) // 5s
        {
            LOG_WARN("[BeaconDetection] 随机割草模式下检测到非草地超过 {} 秒，上报WARN异常状态", NON_GRASS_WARN_THRESHOLD_SECONDS);
            PublishVelocity(0.0, mower_angular_);
            PublishException(SocExceptionLevel::WARNING, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION);

            TriggerExceptionPublishing();

            if (non_grass_duration_.count() >= NON_GRASS_ERROR_THRESHOLD_SECONDS)
            {
                LOG_ERROR("[BeaconDetection] 随机割草模式下检测到非草地超过 {} 秒，上报ERROR异常状态", NON_GRASS_ERROR_THRESHOLD_SECONDS);
                SetAllTaskClose();
                PublishVelocity(0.0, 0.0, 1000);
                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION);
            }
        }
        else
        {
            // 在Run()函数或状态机循环中添加
            if (is_publishing_exception_)
            {
                auto current_time = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - exception_start_time_);

                if (duration.count() < 1000)
                {
                    PublishException(SocExceptionLevel::NONE, SocExceptionValue::NO_EXCEPTION);
                }
                else
                {
                    is_publishing_exception_ = false; // 停止发布
                }
            }

            LOG_INFO_THROTTLE(500, "[BeaconDetection] 开始随机割草模式");
            PerformRandomMowing(); // 执行随机割草操作
        }
    }
}

// 第三阶段：螺旋割草处理
void NavigationMowerAlg::ProcessSpiralMowing()
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        !mcu_triggers_recharge_ &&                           /* MCU 没有回充请求 */
        !mcu_triggers_cross_region_ &&                       /* MCU 没有跨区域请求 */
        !mcu_triggers_mower_ &&                              /* MCU 随机割草请求 */
        mcu_triggers_spiral_mower_)                          /* MCU 螺旋割草请求 */
    {
        // 检查割草期间的非草地状态
        if (!is_on_grass_field_ && non_grass_duration_.count() >= NON_GRASS_WARN_THRESHOLD_SECONDS)
        {
            LOG_WARN("[BeaconDetection] 螺旋割草模式下检测到非草地超过 {} 秒，上报WARN异常状态", NON_GRASS_WARN_THRESHOLD_SECONDS);
            PublishVelocity(0.0, mower_angular_);
            PublishException(SocExceptionLevel::WARNING, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION);

            TriggerExceptionPublishing();

            if (non_grass_duration_.count() >= NON_GRASS_ERROR_THRESHOLD_SECONDS)
            {
                LOG_ERROR("[BeaconDetection] 螺旋割草模式下检测到非草地超过 {} 秒，上报ERROR异常状态", NON_GRASS_ERROR_THRESHOLD_SECONDS);
                SetAllTaskClose();
                PublishVelocity(0.0, 0.0, 1000);
                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION);
            }
        }
        else
        {
            // 在Run()函数或状态机循环中添加
            if (is_publishing_exception_)
            {
                auto current_time = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - exception_start_time_);

                if (duration.count() < 1000)
                {
                    PublishException(SocExceptionLevel::NONE, SocExceptionValue::NO_EXCEPTION);
                }
                else
                {
                    is_publishing_exception_ = false; // 停止发布
                }
            }

            LOG_INFO_THROTTLE(500, "[BeaconDetection] 开始螺旋割草模式");
            PerformSpiralMowing(); // 执行螺旋割草操作
        }
    }
}

// 第四阶段：跨区域处理
void NavigationMowerAlg::ProcessCrossRegionMode(CrossRegionRunningState cross_region_state)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        !mcu_triggers_recharge_ &&                           /* MCU 没有回充请求 */
        mcu_triggers_cross_region_ &&                        /* MCU 跨区域请求 */
        !mcu_triggers_mower_ &&                              /* MCU 随机割草请求 */
        !mcu_triggers_spiral_mower_)                         /* MCU 螺旋割草请求 */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] 开启跨区域模式");

        // 切换到跨区域线程，并更新功能选择
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// 第五阶段：回充处理
void NavigationMowerAlg::ProcessRechargeMode(RechargeRunningState recharge_state)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        mcu_triggers_recharge_ &&                            /* MCU 回充请求 */
        !mcu_triggers_cross_region_ &&                       /* MCU 没有跨区域请求 */
        !mcu_triggers_mower_ &&                              /* MCU 随机割草请求 */
        !mcu_triggers_spiral_mower_)                         /* MCU 螺旋割草请求 */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 开启回充模式");

        // 切换到回充线程，并更新功能选择
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);

        // TODO：考虑是否支持跨区域回充
    }
}

void NavigationMowerAlg::ResetMowerAlgFlags()
{
    // 运行时变量
    is_on_docker_ = false;
    mower_running_state_ = MowerRunningState::STOP;
    thread_control_ = ThreadControl::UNDEFINED; // 不影响回充功能

    is_unstake_mode_completed_ = false;
    is_unstake_success_ = false;
    is_power_connected_ = false;

    mcu_triggers_cross_region_ = false;
    mcu_triggers_recharge_ = false;
    mcu_triggers_mower_ = false;
    mcu_triggers_spiral_mower_ = false;
    mcu_triggers_region_exploration_ = false;
    mcu_triggers_cut_border_ = false;

    is_region_explore_mode_start_ = false;
    is_cut_border_mode_start_ = false;

    mower_completed_ = false;
    frames_.clear();
    is_on_grass_field_ = false;

    UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    UpdateRechargeRunningState(RechargeRunningState::UNDEFINED);

    // 新增
    is_cooldown_active_ = false;
    is_first_enter_last_cooldown_time_ = true;
    last_cooldown_time_ = std::chrono::steady_clock::now();
    edge_perception_drive_duration_ = std::chrono::seconds(0);

    enter_multi_region_exploration_ = false;

    // 单区域
    qr_code_detection_count_ = 1;
    is_first_enter_explore_last_qr_detection_time_ = true;
    last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
    qr_detection_duration_ = std::chrono::seconds(0);
    is_single_area_recharge_ = false;

    // 多区域
    beacon_status_ = BeaconStatus(-1, 0);
    current_mark_id_ = -1;
    is_first_enter_last_mark_detection_time_ = true;
    last_mark_detection_time_ = std::chrono::steady_clock::now();
    mark_detection_duration_ = std::chrono::seconds(0);

    first_detection_beacon_ = true;
    next_paired_beacon_id_ = -1; // 下一对信标id

    region_count_ = 1; // 默认值1个区域

    // 区域探索获取信息
    is_master_region_ = true;
    is_first_region_explore_mode_end_ = true;

    {
        master_region_explore_result_.is_exist = false;
        master_region_explore_result_.area = 0.0;
        master_region_explore_result_.perimeter = 0.0;
        master_region_explore_result_.charge_station_flag = false;
        master_region_explore_result_.beacon_id = -1;

        slave_region_explore_result_.is_exist = false;
        slave_region_explore_result_.area = 0.0;
        slave_region_explore_result_.perimeter = 0.0;
        slave_region_explore_result_.charge_station_flag = false;
        slave_region_explore_result_.beacon_id = -1;
    }

    /**割草非草地异常检测*/
    is_first_non_grass_detection_ = true;
    last_grass_time_ = std::chrono::steady_clock::now();
    non_grass_duration_ = std::chrono::seconds(0);

    exception_start_time_ = std::chrono::steady_clock::now();
    is_publishing_exception_ = false;

    /**自检模式*/
    is_self_checking_ = false;                                 // 是否正在进行自检
    self_check_start_time_ = std::chrono::steady_clock::now(); // 自检开始时间
    is_self_success_ = false;
    is_self_recovery_active_ = false;

    /**cut border*/
    is_first_enter_cut_border_last_qr_detection_time_ = true;
    last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
    is_first_cut_border_mode_end_ = true;

    // 打滑检测
    last_zero_velocity_time_ = std::chrono::steady_clock::now(); // 记录速度接近0的开始时间
    is_velocity_zero_ = false;                                   // 标记当前速度是否接近0

    is_recovery_active_.store(false);

    // 等待异步任务结束（如果已启动）
    if (unstake_future_.valid())
    {
        unstake_future_.wait(); // 或者考虑 wait_for 带超时
    }

    // 出桩
    is_unstaking_ = false;

    //
    last_imu_timestamp_ = 0;
    motion_detection_timestamp_ = 0;
}

void NavigationMowerAlg::SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result)
{
    if (save_qr_data_)
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            qr_detect_x_.push_back(qrcode_loc_result.xyzrpw.x);
            qr_detect_y_.push_back(qrcode_loc_result.xyzrpw.y);
            qr_detect_yaw_.push_back(qrcode_loc_result.xyzrpw.w);
        }
    }
}

/**
 * @brief
 *
 * @param qr_x_set qr_y_set qr_yaw_set
 * @return qr_x_avg qr_y_avg qr_yaw_avg
 */
std::vector<float> NavigationMowerAlg::Process_QRdata(std::vector<float> qr_x_set, std::vector<float> qr_y_set, std::vector<float> qr_yaw_set)
{
    std::sort(qr_x_set.begin(), qr_x_set.end());
    std::sort(qr_y_set.begin(), qr_y_set.end());
    std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    while ((qr_x_set[qr_x_set.size() - 1] - qr_x_set[0]) > 0.3)
    {
        for (int i = 0; i < qr_x_set.size(); i++)
        {
            std::cout << qr_x_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_x_set.size() <= 2)
        {
            break;
        }
        qr_x_set.erase(qr_x_set.begin());
        qr_x_set.pop_back();
        std::sort(qr_x_set.begin(), qr_x_set.end());
    }
    while ((qr_y_set[qr_y_set.size() - 1] - qr_y_set[0]) > 0.3)
    {
        for (int i = 0; i < qr_y_set.size(); i++)
        {
            std::cout << qr_y_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_y_set.size() <= 2)
        {
            break;
        }
        qr_y_set.erase(qr_y_set.begin());
        qr_y_set.pop_back();
        std::sort(qr_y_set.begin(), qr_y_set.end());
    }
    while ((qr_yaw_set[qr_yaw_set.size() - 1] - qr_yaw_set[0]) > 0.3)
    {
        for (int i = 0; i < qr_yaw_set.size(); i++)
        {
            std::cout << qr_yaw_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_yaw_set.size() <= 2)
        {
            break;
        }
        qr_yaw_set.erase(qr_yaw_set.begin());
        qr_yaw_set.pop_back();
        std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    }
    float qr_x_avg = std::accumulate(qr_x_set.begin(), qr_x_set.end(), 0.0) / qr_x_set.size();
    float qr_y_avg = std::accumulate(qr_y_set.begin(), qr_y_set.end(), 0.0) / qr_y_set.size();
    float qr_yaw_avg = std::accumulate(qr_yaw_set.begin(), qr_yaw_set.end(), 0.0) / qr_yaw_set.size();
    LOG_DEBUG("QRCodeData : qr_x_avg:{}, qr_y_avg:{}, qr_yaw_avg:{}.", qr_x_avg, qr_y_avg, qr_yaw_avg);
    std::vector<float> qr_data;
    qr_data.push_back(qr_x_avg);
    qr_data.push_back(qr_y_avg);
    qr_data.push_back(qr_yaw_avg);
    return qr_data;
}

/**
 * @brief 收集二维码数据
 *
 * @return QR data
 */
std::vector<float> NavigationMowerAlg::Collect_QRdata()
{
    PublishVelocity(0, 0, stay_time_);
    save_qr_data_ = true;
    qr_detect_x_.clear();
    qr_detect_y_.clear();
    qr_detect_yaw_.clear();
    int record_times_ = 0;
    int try_times_ = 0;
    while (!(qr_detect_x_.size() >= save_data_num_))
    {
        PublishVelocity(0, 0, stay_time_);
        record_times_++;
        LOG_DEBUG("Stay And Collect Data, record_time :{}", record_times_ * stay_time_);
        if (record_times_ >= (stay_all_time_ / stay_time_))
        {
            LOG_DEBUG("Qrcode Collect Timeout!");
            std::vector<float> qr_error_data{0, 0, 0};
            return qr_error_data;
        }
    }
    save_qr_data_ = false;
    std::vector<float> qr_avg_data = Process_QRdata(qr_detect_x_, qr_detect_y_, qr_detect_yaw_);
    return qr_avg_data;
}

void NavigationMowerAlg::ProcessQRcodeAndUnstake(const std::vector<float> &qrcode_result)
{
    if (fabs(qrcode_result[0]) == 0)
    {
        LOG_DEBUG("QRCodeData Failed And Turn 45 Angle");
        ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_); // 旋转45度背离充电桩
    }
    else
    {
        if (fabs(qrcode_result[1]) > (charge_station_width_ / 2))
        {
            if (qrcode_result[1] > 0)
            {
                LOG_DEBUG("Y > 0 And More Than Charge Width And Turn Angle");
                ControlRotaryMotion(M_PI * angle_proportion_ + fabs(qrcode_result[2]), 0.0, unstake_vel_angular_); // 旋转45度背离充电桩
            }
            else
            {
                LOG_DEBUG("Y < 0 And More Than Charge Width And Turn Angle");
                float angle_0 = std::atan2(fabs(qrcode_result[1]) + (charge_station_width_ / 2),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                ControlRotaryMotion(-fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0,
                                    0.0, unstake_vel_angular_); // 旋转45度背离充电桩
            }
        }
        else
        {
            if (qrcode_result[1] > 0)
            {
                LOG_DEBUG("Y > 0 And Less Than Charge Width And Turn Angle");
                float angle_0 = std::atan2((charge_station_width_ / 2) - fabs(qrcode_result[1]),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                ControlRotaryMotion(fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0,
                                    0.0, unstake_vel_angular_); // 旋转45度背离充电桩
            }
            else
            {
                LOG_DEBUG("Y < 0 And Less Than Charge Width And Turn Angle");
                float angle_0 = std::atan2(fabs(qrcode_result[1]) + (charge_station_width_ / 2),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                ControlRotaryMotion(-fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0, 0.0, unstake_vel_angular_); // 旋转45度背离充电桩
            }
        }
    }
}

void NavigationMowerAlg::PerformUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    LOG_ERROR("[PerformUnstakeMode1] 开始执行出桩操作");

    ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // 退出充电桩
    // ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);  // 旋转45度背离充电桩
    std::vector<float> stable_data = Collect_QRdata();
    LOG_DEBUG("Stable QRCodeData : x:{}, y:{}, yaw:{}.", stable_data[0], stable_data[1], stable_data[2]);
    ProcessQRcodeAndUnstake(stable_data);
    PublishVelocity(0.0, 0.0, 1000); // 停止运动1s

    if (is_on_grass_field_) // 在草地
    {
        is_unstake_mode_completed_ = true; // 出桩完成
        is_unstake_success_ = true;        // 出桩成功

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // 上报成功状态
        LOG_INFO("[PerformUnstakeMode1] 在草地上。出桩模式成功，开始割草模式");
    }
    else // 不在草地
    {
        is_unstake_mode_completed_ = true; // 出桩完成
        is_unstake_success_ = false;       // 出桩失败

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // 上报失败状态
        LOG_ERROR("[PerformUnstakeMode1] 不在草地上。出桩模式失败，上报错误状态");

        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
    }
}

void NavigationMowerAlg::PerformUnstakeModeAsync(const QRCodeLocationResult &qrcode_loc_result)
{
    // 如果已经有一个出桩任务在运行，则不再启动新的任务
    if (unstake_future_.valid() && unstake_future_.wait_for(std::chrono::seconds(0)) != std::future_status::ready)
    {
        LOG_WARN("[PerformUnstakeModeAsync1] 出桩任务已经在运行，跳过新任务启动");
        return;
    }

    // 启动异步任务执行出桩操作
    unstake_future_ = std::async(std::launch::async, [this, qrcode_loc_result]() {
        std::unique_lock<std::mutex> lock(recovery_mutex_);

        // while (is_recovery_active_)
        // {
        //     LOG_INFO("[PerformUnstakeModeAsync1] 检测到恢复操作，挂起出桩任务");
        //     recovery_cv_.wait(lock); // 等待恢复操作完成
        // }

        LOG_ERROR("[PerformUnstakeModeAsync1] 开始执行出桩操作");
        ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // 退出充电桩
        std::vector<float> stable_data = Collect_QRdata();
        LOG_DEBUG("Stable QRCodeData : x:{}, y:{}, yaw:{}.", stable_data[0], stable_data[1], stable_data[2]);
        ProcessQRcodeAndUnstake(stable_data);
        PublishVelocity(0.0, 0.0, 1000); // 停止运动1s

        if (is_on_grass_field_) // 在草地
        {
            is_unstake_mode_completed_ = true; // 出桩完成
            is_unstake_success_ = true;        // 出桩成功

            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // 上报成功状态
            LOG_ERROR("[PerformUnstakeModeAsync1] 在草地上。出桩模式成功，开始割草模式");
        }
        else // 不在草地
        {
            is_unstake_mode_completed_ = true; // 出桩完成
            is_unstake_success_ = false;       // 出桩失败

            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // 上报失败状态
            LOG_ERROR("[PerformUnstakeModeAsync1] 不在草地上。出桩模式失败，上报错误状态");

            PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
        }
    });
}

void NavigationMowerAlg::HandleRecoveryStart()
{
    std::lock_guard<std::mutex> lock(recovery_mutex_);
    is_recovery_active_ = true;
    LOG_INFO("[HandleRecoveryStart1] 恢复操作开始，暂停出桩任务");
}

void NavigationMowerAlg::HandleRecoveryEnd()
{
    {
        std::lock_guard<std::mutex> lock(recovery_mutex_);
        is_recovery_active_ = false;
    }
    recovery_cv_.notify_all(); // 通知挂起的出桩任务继续执行
    LOG_INFO("[HandleRecoveryEnd1] 恢复操作结束，继续出桩任务");
}

void NavigationMowerAlg::SetSlippingStatus(bool is_slipping)
{
    if (vel_publisher_)
    {
        vel_publisher_->SetSlippingStatus(is_slipping); // 设置打滑状态
    }
}

void NavigationMowerAlg::PerformUnstakeMode()
{
    ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // 退出充电桩
    ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);  // 旋转45度背离充电桩

    PublishVelocity(0.0, 0.0, 1000); // 停止运动1s

    if (is_on_grass_field_) // 在草地
    {
        is_unstake_mode_completed_ = true; // 出桩完成
        is_unstake_success_ = true;        // 出桩成功

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // 上报成功状态
        LOG_INFO("[PerformUnstakeMode]  在草地上。出桩模式成功，开始割草模式");
    }
    else // 不在草地
    {
        is_unstake_mode_completed_ = true; // 出桩完成
        is_unstake_success_ = false;       // 出桩失败

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // 上报失败状态
        LOG_ERROR("[PerformUnstakeMode] 不在草地上。出桩模式失败，上报错误状态");

        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
    }
}

/**
 * @brief 判断是否在草地上
 *
 * @param segment_detect_result
 * @return true
 * @return false
 */
bool NavigationMowerAlg::IsGrassField(const GrassDetectStatus &grass_detect_status)
{
    // 将新状态加入队列
    frames_.push_back(grass_detect_status);

    // 如果队列已满（超过10帧），则移除最早的一帧
    if (frames_.size() >= 11)
    {
        frames_.pop_front();
    }

    /**
     * NO_GRASS = 0,                // 无草地（全是障碍物）
     * HAVE_GRASS_NO_OBSTACLE = 1,  // 有草地无障碍物 (全是草地)
     * HAVE_GRASS_HAVE_OBSTACLE = 2 // 有草地有障碍物 （部分草地部分障碍物）
     */

    if (frames_.size() >= 10)
    {
        size_t grass_count = 0; // 草地计数
        for (const auto &status : frames_)
        {
            if (status == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || status == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE) // 有草地无障碍物 (全是草地)
            {
                grass_count++; // 草地
            }
        }
        LOG_DEBUG("[BeaconDetection] 草地计数 grass_count({})", grass_count);

        int grass_count_threshold = 7;
        if (int(grass_count) > grass_count_threshold) // 判断是否为草地
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // 小于10帧，无法判断
    return false;
}

void NavigationMowerAlg::PerformRandomMowing()
{
    thread_control_ = ThreadControl::RANDOM_MOWING_THREAD;
    UpdateFeatureSelection(thread_control_);
}

void NavigationMowerAlg::PerformSpiralMowing()
{
    thread_control_ = ThreadControl::SPIRAL_MOWING_THREAD;
    UpdateFeatureSelection(thread_control_);
}

void NavigationMowerAlg::HandleExploreCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION) // 第四阶段，非草地到草地退出跨区域
    {
        LOG_INFO("[BeaconDetection] 非草地到草地退出跨区域");

        if (edge_mode_direction_ == 1) // 沿边顺时针
        {
            LOG_DEBUG("[CrossRegion] 不考虑顺时针沿边跨区域的情况");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_); // 左转
        }
        else // 沿边逆时针
        {
            LOG_INFO("[BeaconDetection] 沿边逆时针的情况下。向右转一定角度");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // 右转
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection] 关闭跨区域, 切换到回充");
            thread_control_ = ThreadControl::RECHARGE_THREAD; // 回充模式
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1; // 恢复默认值
        }
        else
        {
            LOG_INFO("[BeaconDetection] 关闭跨区域, 切换到沿边");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD; // 沿边模式
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now(); // 信标检测开始计时
            // is_first_enter_last_mark_detection_time_ = true; // 重置计时器。若能跨区域后寻找信标，则需要重置计时器

            if (area_calc_start_callback_)
            {
                area_calc_start_callback_(GetTimestampMs());

                // PublishVelocity(0.0, 0.0, 5000); // 每次给start时间点停止5s
            }
        }

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION) //  第四阶段，信标检测退出跨区域
    {
        LOG_INFO("[BeaconDetection] 信标检测退出跨区域");

        if (edge_mode_direction_ == 1) // 沿边顺时针
        {
            LOG_INFO("[CrossRegion] 不考虑顺时针沿边跨区域的情况");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_); // 左转
        }
        else // 沿边逆时针
        {
            LOG_INFO("[BeaconDetection] 沿边逆时针的情况下。向右转一定角度!");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // 右转
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection] 关闭跨区域, 切换到回充");
            thread_control_ = ThreadControl::RECHARGE_THREAD; // 回充模式
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1; // 恢复默认值
        }
        else
        {
            LOG_INFO("[BeaconDetection] 关闭跨区域, 切换到沿边");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD; // 沿边模式
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now(); // 信标检测开始计时
            // is_first_enter_last_mark_detection_time_ = true; // 重置计时器。若能跨区域后寻找信标，则需要重置计时器

            if (area_calc_start_callback_)
            {
                area_calc_start_callback_(GetTimestampMs());

                // PublishVelocity(0.0, 0.0, 5000); // 每次给start时间点停止5s
            }
        }

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
}

bool NavigationMowerAlg::SetMarkLocationMarkId(int mark_id)
{
    if (set_mark_id_callback_)
    {
        return set_mark_id_callback_(mark_id);
    }
    return false;
}

void NavigationMowerAlg::UpdateCrossRegionRunningState(CrossRegionRunningState state)
{
    if (cross_region_running_state_callback_)
    {
        cross_region_running_state_callback_(state);
    }
}

void NavigationMowerAlg::UpdateRechargeRunningState(RechargeRunningState state)
{
    if (recharge_running_state_callback_)
    {
        recharge_running_state_callback_(state);
    }
}

void NavigationMowerAlg::SetUndockResult(bool completed, bool result, mower_msgs::srv::UndockOperationStatus status)
{
    if (undock_result_callback_)
    {
        undock_result_callback_(completed, result, status);
    }
}

void NavigationMowerAlg::FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx)
{
    for (int i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        if (mark_id_distance_vec[i].distance < mark_distance_threshold_) // 50cm
        {
            shortest_dis_inx = i;
        }
    }
}

void NavigationMowerAlg::PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result)
{
    switch (mark_loc_result.mark_perception_direction)
    {
    case -1: // 偏左，左转
        LOG_DEBUG("基于感知向左调整方向!");
        PublishVelocity(0, mower_angular_);
        break;

    case 0: // 居中，直线
        LOG_DEBUG("基于感知直线调整方向!");
        PublishVelocity(mower_linear_, 0);
        break;

    case 1: // 偏右，右转
        LOG_DEBUG("基于感知向右调整方向!");
        PublishVelocity(0, -mower_angular_);
        break;

    default:
        LOG_DEBUG("基于感知mark_perception_direction标志位错误!");
        PublishVelocity(mower_linear_, 0);
        break;
    }
}

void NavigationMowerAlg::UpdateFeatureSelection(const ThreadControl &thread_control)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData rechgarge{ThreadControl::RECHARGE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData edge_follow{ThreadControl::PERCEPTION_EDGE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData cross_region{ThreadControl::CROSS_REGION_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData random_mower{ThreadControl::RANDOM_MOWING_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData behavior{ThreadControl::BEHAVIOR_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData spiral_mower{ThreadControl::SPIRAL_MOWING_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData escape{ThreadControl::ESCAPE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};

    switch (thread_control)
    {
    case ThreadControl::PERCEPTION_EDGE_THREAD:                               // 沿边：脱困
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);   // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);        // 7. 脱困
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: 沿边：包含脱困");
        break;

    case ThreadControl::CROSS_REGION_THREAD:                                  // 跨区域：包含沿边、脱困
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);   // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 3. 跨区域
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: 跨区域：包含沿边、脱困");
        break;

    case ThreadControl::RECHARGE_THREAD:                                      // 回充：包含沿边、跨区域、脱困
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        // cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);     // 4. 回充
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: 回充：包含沿边、跨区域、脱困");
        break;

    case ThreadControl::RANDOM_MOWING_THREAD:                                 // 随机割草：
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: 随机割草：");
        break;

    case ThreadControl::BEHAVIOR_THREAD:                                      // 恢复行为:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);      // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: 恢复行为:");
        break;

    case ThreadControl::SPIRAL_MOWING_THREAD:                                 // 螺旋割草:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: 螺旋割草:");
        break;

    case ThreadControl::CLOSE_ALL_TASK:                                       // 关闭所有任务:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: 关闭所有任务:");
        break;

    case ThreadControl::UNDEFINED:                                            // 未定义:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: 未定义:");
        break;

    default:
        // random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. 随机割草
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. 沿边
        // cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. 跨区域
        // rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. 回充
        // behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. 恢复
        // spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. 螺旋割草
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. 脱困
        break;
    }

    feature_data.push_back(random_mower);
    feature_data.push_back(edge_follow);
    feature_data.push_back(cross_region);
    feature_data.push_back(rechgarge);
    feature_data.push_back(behavior);
    feature_data.push_back(spiral_mower);
    feature_data.push_back(escape);

    if (feature_select_callback_)
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationMowerAlg::SetMowerAlgParam(const MowerAlgParam &param)
{
    is_enable_unstake_mode_ = param.is_enable_unstake_mode;
    unstake_distance_ = param.unstake_distance;       // 出桩距离 /*param*/
    unstake_adjust_yaw_ = param.unstake_adjust_yaw;   // 出桩调整角度 /*param*/
    unstake_vel_linear_ = param.unstake_vel_linear;   // 出桩线速度 /*param*/
    unstake_vel_angular_ = param.unstake_vel_angular; // 出桩角速度 /*param*/

    // 算法参数
    mower_linear_ = param.mower_linear;   /*param*/
    mower_angular_ = param.mower_angular; /*param*/
    // perception_drive_cooldown_time_ = param.perception_drive_cooldown_time; // 感知驱动冷却时间 5s /*param*/
    edge_mode_direction_ = param.edge_mode_direction;                   // 默认逆时针 -1 /*param*/
    cross_region_adjust_yaw_ = param.cross_region_adjust_yaw;           // 跨区域后调整方位角 /*param*/
    cross_region_adjust_displace_ = param.cross_region_adjust_displace; // 跨区域后调整位移 /*param*/
    mark_distance_threshold_ = param.mark_distance_threshold;           // 1.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    camera_2_center_dis_ = param.camera_2_center_dis;                   // 小车摄像头到旋转中心的距离为0.45 /*param*/

    edge_perception_drive_cooldown_time_threshold_ = param.edge_perception_drive_cooldown_time_threshold; // 10s 沿边感知驱动冷却时间  /*param*/
    qr_detection_cooldown_time_threshold_ = param.qr_detection_cooldown_time_threshold;                   // 60s 沿边感知驱动冷却时间  /*param*/
    mark_detection_cooldown_time_threshold_ = param.mark_detection_cooldown_time_threshold;               // 60s 沿边感知驱动冷却时间  /*param*/
}

void NavigationMowerAlg::SetMowerRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationMowerAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_WARN("[NavigationMowerAlg] Unknown state {}!", static_cast<int>(state));
    }
}

void NavigationMowerAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationMowerAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationMowerAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationMowerAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationMowerAlg::DataConversion(MarkLocationResult &mark_loc_result)
{
    LOG_DEBUG("[BeaconDetection]  坐标转换前 MarkLocation result: detect_status: {} mark_perception_status: {} mark_perception_direction: {} roi_confidence: {} "
              "target_direction : {} markID : {} v_markID_dis.size : {} xyz({} {} {}) yaw({})",
              mark_loc_result.detect_status, mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
              mark_loc_result.roi_confidence, mark_loc_result.target_direction, mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
              mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
              Radians2Degrees(mark_loc_result.xyzrpw.w));

    if (mark_loc_result.roi_confidence >= 60 && mark_loc_result.roi_confidence <= 100) // 60~100
    {
        mark_loc_result.roi_confidence = 1; // 1 表示在ROI区域
    }
    else if (mark_loc_result.roi_confidence < 60 && mark_loc_result.roi_confidence >= 0) // 0~60
    {
        mark_loc_result.roi_confidence = 0; // 0 表示未在ROI区域
    }
    else
    {
        mark_loc_result.roi_confidence = -1; //-1 表示未检测成功
    }

    // 2. 将以mark为右手坐标系，camera相对于mark的坐标转化为 ————》base_link相对于mark的坐标
    if (mark_loc_result.detect_status == 2)
    {
        // 输入camera相对于base_link的固定坐标
        Pose_Mark camera_to_base_link = {camera_2_center_dis_, 0.0, 0.0, 0.0, 0.0, 0.0};

        // 输入camera相对于mark的坐标
        Pose_Mark camera_to_mark = {mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                                    mark_loc_result.xyzrpw.r, mark_loc_result.xyzrpw.p, mark_loc_result.xyzrpw.w};

        // 计算base_link相对于mark的坐标
        Pose_Mark base_link_to_mark = calculateBaseLinkRelativeToMark(camera_to_mark, camera_to_base_link);

        mark_loc_result.xyzrpw.x = base_link_to_mark.x;
        mark_loc_result.xyzrpw.y = base_link_to_mark.y;
        mark_loc_result.xyzrpw.z = base_link_to_mark.z;
        mark_loc_result.xyzrpw.r = base_link_to_mark.roll;
        mark_loc_result.xyzrpw.p = base_link_to_mark.pitch;
        mark_loc_result.xyzrpw.w = base_link_to_mark.yaw;

        LOG_DEBUG("[BeaconDetection]  坐标转换后 MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
                  "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
                  mark_loc_result.detect_status,
                  mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                  mark_loc_result.roi_confidence, mark_loc_result.target_direction,
                  mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                  mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                  Radians2Degrees(mark_loc_result.xyzrpw.w));
    }
}

void NavigationMowerAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationMowerAlg::SetMarkLocationMarkIdCallback(std::function<bool(int)> callback)
{
    set_mark_id_callback_ = callback;
}

void NavigationMowerAlg::SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback)
{
    cross_region_running_state_callback_ = callback;
}

void NavigationMowerAlg::SetRechargeRunningStateCallback(std::function<void(RechargeRunningState)> callback)
{
    recharge_running_state_callback_ = callback;
}

void NavigationMowerAlg::SetUndockResultCallback(std::function<void(bool, bool, mower_msgs::srv::UndockOperationStatus)> callback)
{
    undock_result_callback_ = callback;
}

void NavigationMowerAlg::SetAreaCalcStartCallback(std::function<bool(uint64_t)> callback)
{
    area_calc_start_callback_ = callback;
}

void NavigationMowerAlg::SetAreaCalcStopCallback(std::function<bool(uint64_t, float &, float &)> callback)
{
    area_calc_stop_callback_ = callback;
}

void NavigationMowerAlg::SetRegionExploreResultCallback(std::function<void(RegionExploreResult &)> callback)
{
    region_explore_result_callback_ = callback;
}

void NavigationMowerAlg::SetCutBorderResultCallback(std::function<void(bool, bool)> callback)
{
    cut_border_result_callback_ = callback;
}

void NavigationMowerAlg::SetPerceptionLocalizationAlgCtrlCallback(std::function<void(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &)> callback)
{
    perception_localization_alg_ctrl_callback_ = callback;
}

void NavigationMowerAlg::SetAppTriggersRecharge(bool is_recharge)
{
    mcu_triggers_recharge_ = is_recharge;
    LOG_INFO("[BeaconDetection] MCU 回充请求: mcu_triggers_recharge_({})", int(mcu_triggers_recharge_));
}

void NavigationMowerAlg::SetAppTriggersCrossRegion(bool is_cross_region)
{
    mcu_triggers_cross_region_ = is_cross_region; // MCU 触发跨区线程
    LOG_INFO("[BeaconDetection] MCU 跨区域请求: mcu_triggers_cross_region_({})", int(mcu_triggers_cross_region_));
}

void NavigationMowerAlg::SetAppTriggersMower(bool is_mower)
{
    mcu_triggers_mower_ = is_mower; // MCU 触发随机割草线程
    LOG_INFO("[BeaconDetection] MCU 割草请求: mcu_triggers_mower_({})", int(mcu_triggers_mower_));
}

void NavigationMowerAlg::SetAppTriggersSpiralMower(bool is_mower)
{
    mcu_triggers_spiral_mower_ = is_mower; // MCU 触发螺旋割草线程
    LOG_INFO("[BeaconDetection] MCU 螺旋割草请求: mcu_triggers_spiral_mower_({})", int(mcu_triggers_spiral_mower_));
}

void NavigationMowerAlg::SetAppTriggersRegionExplore(bool is_region_explore)
{
    mcu_triggers_region_exploration_ = is_region_explore; // MCU 触发区域探索线程
    LOG_INFO("[BeaconDetection] MCU 区域探索请求: mcu_triggers_region_exploration_({})", int(mcu_triggers_region_exploration_));
}

void NavigationMowerAlg::SetAppTriggersCutBorder(bool is_cut_border)
{
    mcu_triggers_cut_border_ = is_cut_border; // MCU 触发边切线程
    LOG_INFO("[BeaconDetection] MCU 边切请求: mcu_triggers_cut_border_({})", int(mcu_triggers_cut_border_));
}

void NavigationMowerAlg::ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result)
{
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU 回充请求: mcu_triggers_recharge_({})", int(mcu_triggers_recharge_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU 跨区域请求: mcu_triggers_cross_region_({})", int(mcu_triggers_cross_region_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU (随机)割草请求: mcu_triggers_mower_({})", int(mcu_triggers_mower_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU 螺旋割草请求: mcu_triggers_spiral_mower_({})", int(mcu_triggers_spiral_mower_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU 探索请求: mcu_triggers_region_exploration_({})", int(mcu_triggers_region_exploration_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU 边切请求: mcu_triggers_cut_border_({})", int(mcu_triggers_cut_border_));

#if (TEST == 0)
    /********************************************************出桩打印****************************************************************** */
    if (is_enable_unstake_mode_ && !is_unstake_success_ &&                                                                           /* 开启出桩模式 && 出桩未成功 */
        !is_power_connected_ &&                                                                                                      /* 小车未接电*/
        (mcu_triggers_cut_border_ || mcu_triggers_region_exploration_ || mcu_triggers_mower_)) /* MCU 边切请求 */ /* MCU 探索请求 */ /* MCU 割草请求 */
    {
        if (is_unstake_mode_completed_) // 出桩完成
        {
            LOG_ERROR_THROTTLE(1000, "[BeaconDetection] 出桩失败，上报错误状态!");
        }
        else // 出桩没有完成
        {
            // LOG_WARN_THROTTLE(1000, "[BeaconDetection] 割草机未上电，无法出桩");
        }
    }

    /********************************************************出桩打印****************************************************************** */
#endif

    /********************************************************thread_control_****************************************************************** */
    // switch (thread_control_)
    // {
    // case ThreadControl::PERCEPTION_EDGE_THREAD:
    //     LOG_DEBUG("[BeaconDetection] 执行功能模式: 沿边({})", int(thread_control_));
    //     break;

    // case ThreadControl::CROSS_REGION_THREAD:
    //     LOG_DEBUG("[BeaconDetection] 执行功能模式: 跨区域({})", int(thread_control_));
    //     break;

    // case ThreadControl::RECHARGE_THREAD:
    //     LOG_DEBUG("[BeaconDetection] 执行功能模式: 回充({})", int(thread_control_));
    //     break;

    // case ThreadControl::RANDOM_MOWING_THREAD:
    //     LOG_DEBUG("[BeaconDetection] 执行功能模式: 随机割草({})", int(thread_control_));
    //     break;

    // default:
    //     LOG_DEBUG("[BeaconDetection] 不执行功能模式");
    //     break;
    // }

    /********************************************************mark_loc_result****************************************************************** */

    // LOG_DEBUG("[BeaconDetection]  MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
    //           "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
    //           mark_loc_result.detect_status,
    //           mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
    //           mark_loc_result.roi_confidence, mark_loc_result.target_direction,
    //           mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
    //           mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
    //           Radians2Degrees(mark_loc_result.xyzrpw.w));

    // for (const auto &mark_id_distance : mark_loc_result.mark_id_distance)
    // {
    //     LOG_DEBUG("[BeaconDetection] mark_id_distance 信标和距离: mark_id({}), distance({})",
    //               mark_id_distance.mark_id, mark_id_distance.distance);
    // }
}

void NavigationMowerAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationMowerAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationMowerAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationMowerAlg::CrossRegionDisable()
{
    DealFeatureSelect(ThreadControl::CROSS_REGION_THREAD, false);
}

void NavigationMowerAlg::CrossRegionEnable()
{
    DealFeatureSelect(ThreadControl::CROSS_REGION_THREAD, true);
}

void NavigationMowerAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // 默认旋转方向 1.0左转 -1.0右转
    if (sign > 0)
    {
        LOG_INFO("[ControlRotaryMotion] 旋转方向：左转");
    }
    else
    {
        LOG_INFO("[ControlRotaryMotion] 旋转方向：右转");
    }

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // 转弯持续时间 ms
    LOG_INFO("[ControlRotaryMotion] 旋转角度 = {}", Radians2Degrees(ang_err));
    LOG_INFO("[ControlRotaryMotion] 角速度 = {}, 时间 = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

void NavigationMowerAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                             const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_INFO("[ControlLinearMotion] 直行距离 dis = {}", dis);
    LOG_INFO("[ControlLinearMotion] 直行速度 = {}, 时间 = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

void NavigationMowerAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_iceoryx_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

void NavigationMowerAlg::PublishSlipException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_iceoryx_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_mower_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_iceoryx_exception_->publishAtInterval(exception, std::chrono::milliseconds{1000});
    }
}

void NavigationMowerAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_mower_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation Mower publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}
//==============================================================================
// 区域探索功能处理函数
//==============================================================================

void NavigationMowerAlg::ProcessCutBorderUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* 开启出桩模式 && 出桩未成功 */
        is_power_connected_ &&                             /* 小车接电*/
        mcu_triggers_cut_border_)                          /* MCU 边切请求 */
    {
        LOG_INFO("[ProcessCutBorderUnstakeMode] 边切模式。开始出桩模式");
        PerformUnstakeMode(); // 执行出桩操作
    }
}
void NavigationMowerAlg::ProcessCutBorderUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* 开启出桩模式 && 出桩未成功 */
        is_power_connected_ &&                             /* 小车接电*/
        mcu_triggers_cut_border_)                          /* MCU 边切请求 */
    {
        LOG_INFO("[ProcessCutBorderUnstakeMode] 边切模式。开始出桩模式");
        PerformUnstakeMode(qrcode_loc_result); // 执行出桩操作
    }
}

void NavigationMowerAlg::HandleCutBorderMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state)
{
    // 如果检测到充电桩二维码状态
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        recharge_state == RechargeRunningState::PER_FOUND_QR_CODE) // 已经找到充电桩二维码 /**不可以采取恢复模式 */
    {
        ProcessCutBorderRechargeException(recharge_state);
    }
    // 如果检测到信标二维码状态（跨区域情况）
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON &&
             cross_region_state != CrossRegionRunningState::UNDEFINED) // 已找到信标状态 /**不可以采取恢复模式 */
    {
        ProcessCutBorderCrossRegionException(cross_region_state);
    }
    // 其他异常情况，进入恢复模式
    else
    {
        ProcessRecoveryException();
    }
}

// 处理MCU异常中回充请求逻辑
void NavigationMowerAlg::ProcessCutBorderRechargeException(RechargeRunningState recharge_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] 检测到充电桩二维码");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        mcu_triggers_cut_border_)                            /* MCU 边切请求 */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] 开启回充模式");

        // 切换到回充线程，并更新功能选择
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// 处理MCU异常中跨区域请求逻辑
void NavigationMowerAlg::ProcessCutBorderCrossRegionException(CrossRegionRunningState cross_region_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] 检测到信标二维码");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        mcu_triggers_cut_border_)                            /* MCU 边切请求 */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] 开启跨区域模式");

        // 切换到跨区域线程，并更新功能选择
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

void NavigationMowerAlg::PerformCutBorder(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                                          CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于沿边和未定义模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于沿边和未定义模式");

        bool is_beacon_valid = false; // 默认为信标无效
        ProcessBeaconDetection(mark_loc_result, enter_multi_region_exploration_, is_beacon_valid);

        if (!enter_multi_region_exploration_)
        {
            ProcessSingleAreaCutBorderMode(qrcode_loc_result, enter_multi_region_exploration_);
        }
        else
        {
            ProcessMultiAreaCutBorderMode(mark_loc_result, enter_multi_region_exploration_, is_beacon_valid);
        }

        break;
    }

    case ThreadControl::CROSS_REGION_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于跨区域模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于跨区域模式");

        // 根据跨区域返回的不同状态进行处理
        HandleCutBorderCrossRegionStates(cross_region_state);

        break;
    }

    case ThreadControl::RECHARGE_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于回充模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于回充模式");

        ProcessingCutBorderRecharge(qrcode_loc_result);

        if (is_first_cut_border_mode_end_)
        {
            PublishVelocity(0.0, 0.0, 1000);
            is_first_cut_border_mode_end_ = false;
        }

        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO("[BeaconDetection] 处于Behavior模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于Behavior模式");

        CheckVelocityAndUpdateState(behavior_state);

        break;
    }

    default:
        // LOG_INFO("[BeaconDetection] 处于其他功能模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 处于其他功能模式");

        break;
    }
}

/**
 * @brief 利用充电桩检测判断沿边停止转回充
 *
 * @param mark_loc_result
 * @param qrcode_loc_result
 * @param is_beacon_valid
 */
void NavigationMowerAlg::ProcessSingleAreaCutBorderMode(const QRCodeLocationResult &qrcode_loc_result,
                                                        const bool &enter_multi_region_exploration)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        !enter_multi_region_exploration)                     /* 不进入多区域探索*/
    {
        // LOG_INFO("[BeaconDetection] 开始单区域探索模式");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 开始单区域探索模式");

        if (qrcode_loc_result.detect_status != QRCodeDetectStatus::NO_DETECT_QRCODE)
        {
            auto current_time = std::chrono::steady_clock::now();
            qr_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_qr_cut_border_detection_time_);

            LOG_DEBUG("[BeaconDetection] 充电桩二维码检测冷却的计时（秒）：({})", qr_detection_duration_.count());
            if (qr_detection_duration_.count() > qr_detection_cooldown_time_threshold_) // beacon_detection_cooldown_time_ 增加计时器
            {
                qr_code_detection_count_++;
                last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
                LOG_DEBUG("[BeaconDetection] 检测到充电桩二维码位姿有效，当前检测次数: {}", qr_code_detection_count_);
            }
        }

        // 利用二维码检测判断沿边停止转回充
        if (qr_code_detection_count_ >= 2)
        {
            LOG_INFO("[BeaconDetection] 检测到回充二维码位姿有效两次，进入回充模式");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            is_single_area_recharge_ = true;

            // 重置状态
            qr_code_detection_count_ = 1;
        }
    }
}

void NavigationMowerAlg::ProcessMultiAreaCutBorderMode(const MarkLocationResult &mark_loc_result,
                                                       const bool &enter_multi_region_exploration,
                                                       bool &is_beacon_valid)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* 未开启出桩模式 || 出桩成功  (第一阶段完成)*/
        enter_multi_region_exploration)                      /* 进入多区域探索*/
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] 开始多区域探索模式");

        if (is_beacon_valid) // 信标有效
        {
            // 重置冷却时间戳，并激活冷却机制
            last_cooldown_time_ = std::chrono::steady_clock::now();
            is_cooldown_active_ = true;
            // ResetAndActivateCooldown();

            LOG_INFO("[BeaconDetection] 检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
            LOG_INFO("[BeaconDetection] 当前检测mark_id: {}", current_mark_id_);

            // 只在首次进入时初始化
            if (is_first_enter_last_mark_detection_time_)
            {
                last_mark_detection_time_ = std::chrono::steady_clock::now(); // 信标检测开始计时
                is_first_enter_last_mark_detection_time_ = false;
                LOG_DEBUG("[BeaconDetection] 信标检测开始计时");
            }

            auto current_time = std::chrono::steady_clock::now();
            mark_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_mark_detection_time_);

            auto current_time_sec = std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
            auto last_mark_detection_time_sec = std::chrono::duration_cast<std::chrono::seconds>(last_mark_detection_time_.time_since_epoch());
            LOG_WARN("[BeaconDetection]  当前时间戳（秒）current_time_sec({})", current_time_sec.count());
            LOG_WARN("[BeaconDetection] 上一时间戳（秒）last_mark_detection_time_sec ({})", last_mark_detection_time_sec.count());
            LOG_DEBUG("[BeaconDetection] 信标检测冷却的计时（秒）：({})", mark_detection_duration_.count());
            if (mark_detection_duration_.count() > mark_detection_cooldown_time_threshold_)
            {
                if (current_mark_id_ == beacon_status_.mark_id) // 同一个mark_id
                {
                    beacon_status_.beacon_look_count++;
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection] 检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection] 同一个mark_id, 当前检测mark_id: {}", current_mark_id_);
                }
                else // 不同mark_id
                {
                    beacon_status_ = BeaconStatus(current_mark_id_, 1);
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection] 检测到信标二维码位姿有效，当前检测次数: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection] 不同mark_id, 当前检测mark_id: {}", current_mark_id_);
                    LOG_WARN("[BeaconDetection] 不同mark_id, 上一检测mark_id: {}", beacon_status_.mark_id);
                }
            }
        }

        if (beacon_status_.beacon_look_count >= 2)
        {
            // 启动跨区域进程
            LOG_INFO("[BeaconDetection] 信标检测超过两次，启动跨区域进程");

            thread_control_ = ThreadControl::CROSS_REGION_THREAD;
            UpdateFeatureSelection(thread_control_);
            EdgeFollowDisable();
            is_single_area_recharge_ = false;

            // 重置状态
            next_paired_beacon_id_ = PairNumber(current_mark_id_);
            beacon_status_ = BeaconStatus(next_paired_beacon_id_, 1);
            LOG_INFO("[BeaconDetection]下一对信标id为 {}", next_paired_beacon_id_);
        }
        else
        {
            // 继续沿边
            LOG_INFO("[BeaconDetection] 信标检测未超过两次，继续沿边");

            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();
        }
    }
}

void NavigationMowerAlg::HandleCutBorderCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION) // 第四阶段，非草地到草地退出跨区域
    {
        LOG_INFO("[BeaconDetection] 非草地到草地退出跨区域");

        if (edge_mode_direction_ == 1) // 沿边顺时针
        {
            LOG_INFO("[CrossRegion] 不考虑顺时针沿边跨区域的情况");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_); // 左转
        }
        else // 沿边逆时针
        {
            LOG_INFO("[BeaconDetection] 沿边逆时针的情况下。向右转一定角度");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // 右转
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection] 关闭跨区域, 切换到回充");
            thread_control_ = ThreadControl::RECHARGE_THREAD; // 回充模式
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1; // 恢复默认值
        }
        else
        {
            LOG_INFO("[BeaconDetection] 关闭跨区域, 切换到沿边");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD; // 沿边模式
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now(); // 信标检测开始计时
            // is_first_enter_last_mark_detection_time_ = true; // 重置计时器。若能跨区域后寻找信标，则需要重置计时器
        }

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION) //  第四阶段，信标检测退出跨区域
    {
        LOG_INFO("[BeaconDetection] 信标检测退出跨区域");

        if (edge_mode_direction_ == 1) // 沿边顺时针
        {
            LOG_INFO("[CrossRegion] 不考虑顺时针沿边跨区域的情况");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_); // 左转
        }
        else // 沿边逆时针
        {
            LOG_INFO("[BeaconDetection] 沿边逆时针的情况下。向右转一定角度!");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // 右转
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection] 关闭跨区域, 切换到回充");
            thread_control_ = ThreadControl::RECHARGE_THREAD; // 回充模式
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1; // 恢复默认值
        }
        else
        {
            LOG_INFO("[BeaconDetection] 关闭跨区域, 切换到沿边");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD; // 沿边模式
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now(); // 信标检测开始计时
            // is_first_enter_last_mark_detection_time_ = true; // 重置计时器。若能跨区域后寻找信标，则需要重置计时器
        }

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
}

void NavigationMowerAlg::ProcessingCutBorderRecharge(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_single_area_recharge_) // 单区域回充，基于回充条件上报
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE) // 可以计算充电桩二维码位姿
        {
            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < recharge_distance_threshold_)
            {
                if (cut_border_result_callback_)
                {
                    cut_border_result_callback_(true, true);
                }

                is_cut_border_mode_start_ = false;
            }
        }
    }
    else // 多区域回充，直接上报
    {
        if (cut_border_result_callback_)
        {
            cut_border_result_callback_(true, true);
        }

        is_cut_border_mode_start_ = false;
    }
}

//==============================================================================
// 区域探索功能处理函数
//==============================================================================

void NavigationMowerAlg::TriggerExceptionPublishing()
{
    exception_start_time_ = std::chrono::steady_clock::now();
    is_publishing_exception_ = true;
}

void NavigationMowerAlg::InitStuckDetectionRecovery()
{
    LOG_INFO("[MowerAlg] 初始化脱困检测和恢复系统");

    // 设置脱困恢复参数
    StuckRecoveryParam param;
    param.wheel_radius = wheel_radius_;
    param.wheel_base = wheel_base_;
    param.enable_data_logging = true;
    param.log_file_path = "/userdata/log/stuck_recovery_data.csv";

    // 创建脱困检测恢复实例
    stuck_detection_recovery_ = std::make_unique<StuckDetectionRecovery>(param);

    // 设置速度发布器 - 转换unique_ptr为shared_ptr
    stuck_detection_recovery_->SetVelocityPublisher(std::shared_ptr<VelocityPublisher>(vel_publisher_.get(), [](VelocityPublisher *) {}));

    // 初始化
    stuck_detection_recovery_->Initialize();
}

void NavigationMowerAlg::DeinitStuckDetectionRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] 关闭脱困检测和恢复系统");
        stuck_detection_recovery_->Shutdown();
        stuck_detection_recovery_.reset();
    }
}

bool NavigationMowerAlg::IsStuckDetected()
{
    if (stuck_detection_recovery_)
    {
        return stuck_detection_recovery_->IsStuck();
    }
    return false;
}

bool NavigationMowerAlg::StartStuckRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] 开始脱困恢复");
        return stuck_detection_recovery_->StartRecovery();
    }
    return false;
}

void NavigationMowerAlg::StopStuckRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] 停止脱困恢复");
        stuck_detection_recovery_->StopRecovery();
    }
}

bool NavigationMowerAlg::IsStuckRecoveryActive()
{
    if (stuck_detection_recovery_)
    {
        return stuck_detection_recovery_->IsRecoveryActive();
    }
    return false;
}

void NavigationMowerAlg::SetStuckDetectionActive(bool active)
{
    if (stuck_detection_recovery_)
    {
        if (active)
        {
            stuck_detection_recovery_->StartDetection();
        }
        else
        {
            stuck_detection_recovery_->StopDetection();
        }
    }
}

} // namespace fescue_iox