#ifndef STUCK_DETECTION_RECOVERY_HPP
#define STUCK_DETECTION_RECOVERY_HPP

#include "data_type.hpp"
#include "utils/logger.hpp"
#include "velocity_publisher.hpp"

#include <atomic>
#include <chrono>
#include <deque>
#include <fstream>
#include <memory>
#include <mutex>
#include <thread>
#include <vector>

namespace fescue_iox
{

// 脱困恢复模式枚举
enum class RecoveryMode
{
    NONE = 0,           // 无恢复
    ROTATE_LEFT,        // 左转
    ROTATE_RIGHT,       // 右转
    FORWARD,            // 前进
    BACKWARD,           // 后退
    SINGLE_WHEEL_LEFT,  // 左轮单独旋转
    SINGLE_WHEEL_RIGHT, // 右轮单独旋转
    ALTERNATING_PUSH    // 交替推进
};

// 运动数据结构
struct MovementData
{
    uint64_t timestamp;         // 时间戳 (ms)
    float linear_displacement;  // 线性位移 (m)
    float angular_displacement; // 角度位移 (rad)
    float linear_velocity;      // 线性速度 (m/s)
    float angular_velocity;     // 角速度 (rad/s)

    MovementData()
        : timestamp(0)
        , linear_displacement(0.0f)
        , angular_displacement(0.0f)
        , linear_velocity(0.0f)
        , angular_velocity(0.0f)
    {
    }
};

// 时间窗口检测结果
struct WindowDetectionResult
{
    bool is_stuck;               // 是否被困
    float total_displacement;    // 总位移量
    float total_rotation;        // 总旋转量
    uint64_t window_duration_ms; // 窗口持续时间 (ms)
    bool data_insufficient;      // 数据是否不足

    WindowDetectionResult()
        : is_stuck(false)
        , total_displacement(0.0f)
        , total_rotation(0.0f)
        , window_duration_ms(0)
        , data_insufficient(false)
    {
    }
};

// 脱困恢复参数
struct StuckRecoveryParam
{
    // 检测参数
    float min_displacement_threshold_30s = 2.0f;   // 30秒最小位移阈值 (m)
    float min_rotation_threshold_30s = 2.0f;       // 30秒最小旋转阈值 (rad)
    float min_displacement_threshold_2min = 12.0f; // 2分钟最小位移阈值 (m)
    float min_rotation_threshold_2min = 10.0f;     // 2分钟最小旋转阈值 (rad)
    float min_displacement_threshold_5min = 20.0f; // 5分钟最小位移阈值 (m)
    float min_rotation_threshold_5min = 20.0f;     // 5分钟最小旋转阈值 (rad)

    // 恢复参数
    float initial_linear_speed = 0.3f;  // 初始线速度 (m/s)
    float initial_angular_speed = 0.5f; // 初始角速度 (rad/s)
    float max_linear_speed = 1.0f;      // 最大线速度 (m/s)
    float max_angular_speed = 1.2f;     // 最大角速度 (rad/s)
    float speed_increment = 0.1f;       // 速度递增量

    // 恢复动作持续时间
    uint64_t recovery_action_duration_ms = 5000; // 每个恢复动作持续时间 (ms) 5秒
    // uint64_t recovery_pause_duration_ms = 500;   // 恢复动作间暂停时间 (ms)
    uint64_t max_recovery_duration_ms = 300000; // 最大恢复持续时间 (ms) 30分钟

    // 车辆参数
    float wheel_radius = 0.1f; // 车轮半径 (m)
    float wheel_base = 0.335f; // 轴距 (m)

    // 数据保存
    bool enable_data_logging = true;                                     // 是否启用数据记录
    std::string log_file_path = "/userdata/log/stuck_recovery_data.csv"; // 日志文件路径
};

class StuckDetectionRecovery
{
public:
    explicit StuckDetectionRecovery(const StuckRecoveryParam &param);
    ~StuckDetectionRecovery();

    // 初始化和清理
    void Initialize();
    void Shutdown();

    // 动态启动/停止检测
    void StartDetection();
    void StopDetection();
    bool IsDetectionActive() const;

    // 数据输入接口
    void SetImuData(const ImuData &imu_data);
    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);

    // 主要功能接口
    bool IsStuck();                // 检测是否被困
    bool StartRecovery();          // 开始脱困恢复
    void StopRecovery();           // 停止脱困恢复
    bool IsRecoveryActive() const; // 是否正在恢复中

    // 设置回调函数
    void SetVelocityPublisher(std::shared_ptr<VelocityPublisher> vel_publisher);

    // 参数设置
    void SetParam(const StuckRecoveryParam &param);
    StuckRecoveryParam GetParam() const;

    // 状态查询
    RecoveryMode GetCurrentRecoveryMode() const;
    std::vector<WindowDetectionResult> GetDetectionResults() const;

private:
    // 核心检测和恢复线程
    void DetectionThread();
    void RecoveryThread();

    // 运动数据处理
    void ProcessImuData(const ImuData &imu_data);
    void ProcessMotorData(const MotorSpeedData &motor_data);
    void UpdateMovementData();

    // 滑动窗口检测
    WindowDetectionResult CheckWindow(uint64_t window_duration_ms,
                                      float min_displacement,
                                      float min_rotation);
    bool IsStuckInMultipleWindows();

    // 恢复策略
    void ExecuteRecoveryAction(RecoveryMode mode, float linear_speed, float angular_speed);
    RecoveryMode GetNextRecoveryMode();
    void ProgressiveSpeedAdjustment();

    // 实时运动监测
    bool HasMovementDuringRecovery();
    void ResetMovementTracking();

    // 数据记录
    void InitializeDataLogging();
    void LogData(const MovementData &data);
    void LogFilteringData(uint64_t timestamp, float raw_angular_vel, float filtered_angular_vel,
                          float raw_linear_accel, float filtered_linear_accel);
    void CloseDataLogging();

    // 工具函数
    uint64_t GetCurrentTimestamp() const;
    float CalculateDisplacement(float linear_vel, float dt);
    float CalculateRotation(float angular_vel, float dt);
    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);

    // 滤波函数
    float ApplyLowPassFilter(float new_value, float &filtered_value, float alpha);
    void InitializeFilters(float initial_angular_velocity, float initial_linear_acceleration);

private:
    // 参数
    StuckRecoveryParam param_;

    // 线程控制
    std::atomic_bool detection_running_{false};
    std::atomic_bool recovery_running_{false};
    std::atomic_bool detection_active_{false}; // 控制检测是否激活
    std::thread detection_thread_;
    std::thread recovery_thread_;

    // 数据互斥锁
    std::mutex imu_mutex_;
    std::mutex motor_mutex_;
    std::mutex movement_mutex_;

    // 传感器数据
    ImuData latest_imu_data_;
    MotorSpeedData latest_motor_data_;
    bool imu_data_valid_{false};
    bool motor_data_valid_{false};

    // 运动数据队列 (滑动窗口)
    std::deque<MovementData> movement_history_;
    MovementData current_movement_;

    // 检测状态
    std::atomic_bool is_stuck_{false};
    std::vector<WindowDetectionResult> detection_results_;

    // 恢复状态
    std::atomic_bool recovery_active_{false};
    RecoveryMode current_recovery_mode_{RecoveryMode::NONE};
    float current_linear_speed_;
    float current_angular_speed_;
    uint64_t recovery_start_time_;
    uint64_t last_action_time_;
    int recovery_cycle_count_;

    // 实时运动监测
    MovementData recovery_start_position_;
    float recovery_displacement_threshold_{0.1f}; // 恢复期间最小位移阈值

    // 速度发布器
    std::shared_ptr<VelocityPublisher> vel_publisher_;

    // 数据记录
    std::ofstream data_log_file_;
    std::ofstream filter_log_file_;
    bool data_logging_initialized_{false};

    // IMU处理相关
    bool is_first_imu_{true};
    std::chrono::steady_clock::time_point last_imu_time_;
    uint64_t last_imu_timestamp_{0};
    bool is_bias_calibrated_{false};
    float bias_z_{0.0f};                            // 角速度零偏
    float bias_linear_x_{0.0f};                     // 线性加速度零偏
    float bias_threshold_{0.02f};                   // 角速度阈值
    float bias_threshold_linear_{0.2f};             // 线性加速度阈值
    std::vector<float> calibration_samples_;        // 角速度校准样本
    std::vector<float> calibration_samples_linear_; // 线性加速度校准样本
    static constexpr int CALIBRATION_SAMPLES = 300; // 1秒数据用于校准

    // 累积位移和旋转
    float linear_velocity_integrated_{0.0f}; // IMU积分得到的线速度

    // 低通滤波器相关
    float filter_alpha_{0.01f};                // 滤波器系数 (0-1, 越小滤波越强)
    float filtered_angular_velocity_{0.0f};    // 滤波后的角速度
    float filtered_linear_acceleration_{0.0f}; // 滤波后的线性加速度
    bool filter_initialized_{false};           // 滤波器是否已初始化
};

} // namespace fescue_iox

#endif // STUCK_DETECTION_RECOVERY_HPP
